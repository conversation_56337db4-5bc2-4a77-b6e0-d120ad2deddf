doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title WEB BÁN SÁCH TRUYỆN NHÓM 9
    link(rel='stylesheet' href='/public/css/home.css')
    link(rel='stylesheet' href='/public/css/search.css')
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css')
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/fontawesome.min.css')
  body
    header
      .header
        img(src='/public/img/download1.webp' alt='Banner')
      .menu
        ul
          li
            h3 BOOKSTORE
          li
            a(href='/')
              i.fa-solid.fa-house
          li
            a(href='#')
              i.fa-solid.fa-list
            ul.sub-menu
              if categories && categories.length > 0
                each category in categories
                  li
                    a(href=`/books/category/${category._id}`)= category.name.toUpperCase()
              else
                li
                  a(href='#') Chưa có danh mục nào
          li
            form(action="/search" method="GET" id="search-form")
              div.search-input-container(style="position: relative;")
                input(type='text' placeholder='Tìm kiếm sản phẩm...' name="q" id="search-input" autocomplete="off")
                button(type="submit")
                  i.fa-solid.fa-magnifying-glass
                div.search-suggestions(id="search-suggestions" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 5px 5px; box-shadow: 0 4px 15px rgba(0,0,0,0.15); z-index: 1000; display: none; max-height: 400px; overflow-y: auto;")
          li
              a(href='/admin')
                i.fa-solid.fa-rectangle-ad
          li
            if user
              a(href='/customer')
                i.fa-solid.fa-circle-user
                span #{user.username}
            else
              a(href='/auth/login')
                i.fa-solid.fa-circle-user
                span Đăng nhập
          if user
            li
              a(href='/auth/logout')
                i.fa-solid.fa-sign-out-alt
                span Đăng xuất
          li
            a(href='/cart')
              i.fa-solid.fa-cart-shopping
      .anh
        .anh1
          #slide-container.anh2
            img(src='/public/img/bar1.webp' width='1220' alt='Slide 1')
            img(src='/public/img/bar2.webp' width='1220' alt='Slide 2')
            img(src='/public/img/bar3.webp' width='1220' alt='Slide 3')
          button#prevBtn.prev-btn
            i.fa-solid.fa-angle-left
          button#nextBtn.next-btn
            i.fa-solid.fa-angle-right

    section.slider
      .slider3
        h2 SÁCH BÁN CHẠY
        .banchay
          h1 Sản phẩm bán chạy
          if banChay && banChay.length > 0
            each book in banChay
              .banchay1
                a(href=`/books/${book._id}`)
                  img(src=book.image || '/public/img/default.jpg' alt=book.title)
                  h3 Giá: #{book.price.toLocaleString()}đ
                  p= book.title
      .slider2
        h2 SÁCH MỚI
        .banchay
          if sachMoi && sachMoi.length > 0
            each book in sachMoi
              .banchay1
                a(href=`/books/${book._id}`)
                  img(src=book.image || '/public/img/default.jpg' alt=book.title)
                  h3 Giá: #{book.price.toLocaleString()}đ
                  p= book.title

      .slider3
        h2 COMBO SÁCH
        .banchay
          if comboSach && comboSach.length > 0
            each book in comboSach
              .banchay1
                a(href=`/books/${book._id}`)
                  img(src=book.image || '/public/img/default.jpg' alt=book.title)
                  h3 Giá: #{book.price.toLocaleString()}đ
                  p= book.title

      .slider3
        h2 MANGA - COMIC
        .banchay
          if manga && manga.length > 0
            each book in manga
              .banchay1
                a(href=`/books/${book._id}`)
                  img(src=book.image || '/public/img/default.jpg' alt=book.title)
                  h3 Giá: #{book.price.toLocaleString()}đ
                  p= book.title

      .slider3
        h2 TÂM LÝ HỌC
        .banchay
          if tamlyHoc && tamlyHoc.length > 0
            each book in tamlyHoc
              .banchay1
                a(href=`/books/${book._id}`)
                  img(src=book.image || '/public/img/default.jpg' alt=book.title)
                  h3 Giá: #{book.price.toLocaleString()}đ
                  p= book.title

      .slider3
        h2 SELFHELP
        .banchay
          if selfHelp && selfHelp.length > 0
            each book in selfHelp
              .banchay1
                a(href=`/books/${book._id}`)
                  img(src=book.image || '/public/img/default.jpg' alt=book.title)
                  h3 Giá: #{book.price.toLocaleString()}đ
                  p= book.title



    footer
      .footer
        h1 WEB BÁN SÁCH
        .chantrang
          h3 Tài kkhoản
          ul
            li
              a(href='/customer') Thông tin tài khoản
            li
              a(href='/auth/register') Đăng ký
            li
              a(href='/auth/login') Đăng nhập
        .chantrang
          h3 Liên Hệ
          ul
            li Email: <EMAIL>
            li SĐt: 08545071970854507197
    script(src='/public/js/home.js')
    script(src='/public/js/search.js')