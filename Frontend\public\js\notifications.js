// Notification System for Order Status
document.addEventListener('DOMContentLoaded', function() {
    const notificationBtn = document.getElementById('notification-btn');
    const notificationDropdown = document.getElementById('notification-dropdown');
    const notificationCount = document.getElementById('notification-count');
    const notificationList = document.getElementById('notification-list');
    const markAllReadBtn = document.getElementById('mark-all-read');
    
    let notifications = [];
    let isDropdownOpen = false;
    
    if (!notificationBtn || !notificationDropdown) {
        console.log('Notification elements not found');
        return;
    }
    
    // Toggle dropdown
    notificationBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        toggleDropdown();
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!notificationDropdown.contains(e.target) && !notificationBtn.contains(e.target)) {
            closeDropdown();
        }
    });
    
    // Mark all as read
    markAllReadBtn.addEventListener('click', function() {
        markAllAsRead();
    });
    
    function toggleDropdown() {
        if (isDropdownOpen) {
            closeDropdown();
        } else {
            openDropdown();
        }
    }
    
    function openDropdown() {
        isDropdownOpen = true;
        notificationDropdown.classList.add('show');
        loadNotifications();
    }
    
    function closeDropdown() {
        isDropdownOpen = false;
        notificationDropdown.classList.remove('show');
    }
    
    // Load notifications from API
    async function loadNotifications() {
        try {
            showLoading();
            
            const response = await fetch('/orders/notifications');
            
            if (!response.ok) {
                if (response.status === 401) {
                    showError('Vui lòng đăng nhập để xem thông báo');
                    return;
                }
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                notifications = data.notifications;
                updateNotificationCount(data.unreadCount);
                displayNotifications(notifications);
            } else {
                showError('Không thể tải thông báo');
            }
            
        } catch (error) {
            console.error('Error loading notifications:', error);
            showError('Lỗi kết nối. Vui lòng thử lại.');
        }
    }
    
    function showLoading() {
        notificationList.innerHTML = `
            <div class="notification-loading">
                <i class="fa-solid fa-spinner fa-spin"></i>
                <span>Đang tải thông báo...</span>
            </div>
        `;
    }
    
    function showError(message) {
        notificationList.innerHTML = `
            <div class="notification-error">
                <i class="fa-solid fa-exclamation-triangle"></i>
                <span>${message}</span>
            </div>
        `;
    }
    
    function displayNotifications(notifications) {
        if (notifications.length === 0) {
            notificationList.innerHTML = `
                <div class="notification-empty">
                    <i class="fa-solid fa-bell-slash"></i>
                    <span>Không có thông báo nào</span>
                </div>
            `;
            return;
        }
        
        let html = '';
        notifications.forEach(notification => {
            const timeAgo = getTimeAgo(notification.updatedAt);
            const statusText = getStatusText(notification.status);
            const statusClass = getStatusClass(notification.type);
            
            html += `
                <div class="notification-item ${notification.isRead ? 'read' : 'unread'}" 
                     data-order-id="${notification.id}">
                    <div class="notification-icon ${statusClass}">
                        ${getStatusIcon(notification.type)}
                    </div>
                    <div class="notification-content">
                        <div class="notification-message">${notification.message}</div>
                        <div class="notification-details">
                            <span class="notification-status">${statusText}</span>
                            <span class="notification-amount">${notification.totalAmount.toLocaleString('vi-VN')} đ</span>
                            <span class="notification-items">${notification.itemCount} sản phẩm</span>
                        </div>
                        <div class="notification-time">${timeAgo}</div>
                    </div>
                    ${notification.firstItem ? `
                        <div class="notification-image">
                            <img src="${notification.firstItem.image}" alt="${notification.firstItem.title}" 
                                 onerror="this.src='/public/img/default-book.jpg'">
                        </div>
                    ` : ''}
                </div>
            `;
        });
        
        notificationList.innerHTML = html;
        
        // Add click events to notifications
        const notificationItems = notificationList.querySelectorAll('.notification-item');
        notificationItems.forEach(item => {
            item.addEventListener('click', function() {
                const orderId = this.getAttribute('data-order-id');
                window.location.href = `/orders/my-orders?highlight=${orderId}`;
            });
        });
    }
    
    function updateNotificationCount(count) {
        if (count > 0) {
            notificationCount.textContent = count > 99 ? '99+' : count;
            notificationCount.style.display = 'block';
            notificationBtn.classList.add('has-notifications');
        } else {
            notificationCount.style.display = 'none';
            notificationBtn.classList.remove('has-notifications');
        }
    }
    
    function getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'Vừa xong';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} phút trước`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} giờ trước`;
        if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} ngày trước`;
        return date.toLocaleDateString('vi-VN');
    }
    
    function getStatusText(status) {
        const statusMap = {
            'pending': 'Chờ xác nhận',
            'confirmed': 'Đã xác nhận',
            'shipping': 'Đang giao',
            'delivered': 'Đã giao',
            'cancelled': 'Đã hủy'
        };
        return statusMap[status] || 'Không xác định';
    }
    
    function getStatusClass(type) {
        const classMap = {
            'success': 'notification-success',
            'warning': 'notification-warning',
            'error': 'notification-error',
            'info': 'notification-info'
        };
        return classMap[type] || 'notification-info';
    }
    
    function getStatusIcon(type) {
        const iconMap = {
            'success': '<i class="fa-solid fa-check-circle"></i>',
            'warning': '<i class="fa-solid fa-clock"></i>',
            'error': '<i class="fa-solid fa-times-circle"></i>',
            'info': '<i class="fa-solid fa-truck"></i>'
        };
        return iconMap[type] || '<i class="fa-solid fa-info-circle"></i>';
    }
    
    function markAllAsRead() {
        // Update UI immediately
        const unreadItems = notificationList.querySelectorAll('.notification-item.unread');
        unreadItems.forEach(item => {
            item.classList.remove('unread');
            item.classList.add('read');
        });
        
        updateNotificationCount(0);
        
        // TODO: Send API request to mark as read on server
        // This would require implementing read status in backend
    }
    
    // Auto-refresh notifications every 5 minutes
    setInterval(function() {
        if (isDropdownOpen) {
            loadNotifications();
        }
    }, 5 * 60 * 1000);
    
    // Load initial notification count
    loadInitialCount();
    
    async function loadInitialCount() {
        try {
            const response = await fetch('/orders/notifications');
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    updateNotificationCount(data.unreadCount);
                }
            }
        } catch (error) {
            console.error('Error loading initial notification count:', error);
        }
    }
});
