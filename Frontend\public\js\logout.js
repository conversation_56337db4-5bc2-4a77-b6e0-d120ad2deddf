document.addEventListener("DOMContentLoaded", function () {
  const logOut = document.querySelector(".log-out");
  const logOutForm = document.querySelector(".logout-form");
  const cancelBtn = document.querySelector(".cancel");
  const confirmBtn = document.querySelector(".confirm");

  if (logOut && logOutForm) {
    logOut.addEventListener("click", function () {
      logOutForm.classList.add("open");
    });
  }

  if (cancelBtn) {
    cancelBtn.addEventListener("click", function (e) {
      e.preventDefault();
      logOutForm.classList.remove("open");
    });
  }

  if (confirmBtn) {
    confirmBtn.addEventListener("click", function () {
      // Redirect to logout page
      window.location.href = "/auth/logout";
    });
  }
});