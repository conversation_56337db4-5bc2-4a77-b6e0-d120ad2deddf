// Search functionality with autocomplete
document.addEventListener('DOMContentLoaded', function() {
    console.log('Search script loaded');

    // Xóa search history cũ nếu có
    try {
        localStorage.removeItem('searchHistory');
    } catch (error) {
        console.log('No search history to remove');
    }
    const searchInput = document.getElementById('search-input');
    const searchSuggestions = document.getElementById('search-suggestions');
    const searchForm = document.getElementById('search-form');

    console.log('Search elements:', { searchInput, searchSuggestions, searchForm });

    let currentSuggestionIndex = -1;
    let suggestions = [];
    let searchTimeout;

    if (!searchInput || !searchSuggestions) {
        console.log('Search elements not found!');
        return;
    }

    // Debounced search function
    function debounceSearch(query) {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (query.length >= 1) {
                fetchSuggestions(query);
            } else {
                hideSuggestions();
            }
        }, 200); // Giảm delay xuống 200ms để phản hồi n<PERSON>h hơn
    }

    // Fetch suggestions from API
    async function fetchSuggestions(query) {
        try {
            console.log('Fetching suggestions for:', query);
            const response = await fetch(`/search/suggestions?q=${encodeURIComponent(query)}`);
            console.log('Response status:', response.status);
            if (response.ok) {
                suggestions = await response.json();
                console.log('Suggestions received:', suggestions);
                displaySuggestions(suggestions);
            } else {
                console.log('Response not ok:', response.status, response.statusText);
            }
        } catch (error) {
            console.error('Error fetching suggestions:', error);
            hideSuggestions();
        }
    }

    // Display suggestions
    function displaySuggestions(suggestions) {
        if (suggestions.length === 0) {
            hideSuggestions();
            return;
        }

        let html = '';
        suggestions.forEach((book, index) => {
            const imageUrl = book.image || '/public/img/default-book.jpg';
            const price = book.price ? book.price.toLocaleString('vi-VN') + ' đ' : 'Liên hệ';
            const matchTypeText = book.matchType === 'author' ? 'Tác giả' :
                                 book.matchType === 'publisher' ? 'Nhà xuất bản' : 'Sách';

            html += `
                <div class="suggestion-item book-suggestion" data-index="${index}" data-book-id="${book._id}" data-text="${book.title}">
                    <div class="book-image">
                        <img src="${imageUrl}" alt="${book.title}" onerror="this.src='/public/img/default-book.jpg'">
                    </div>
                    <div class="book-info">
                        <div class="book-title">${highlightMatch(book.title, searchInput.value)}</div>
                        ${book.author ? `<div class="book-author">Tác giả: ${highlightMatch(book.author, searchInput.value)}</div>` : ''}
                        <div class="book-price">${price}</div>
                        <div class="match-type">${matchTypeText}</div>
                    </div>
                </div>
            `;
        });

        searchSuggestions.innerHTML = html;
        searchSuggestions.classList.add('show');
        currentSuggestionIndex = -1;

        // Add click event listeners
        const suggestionItems = searchSuggestions.querySelectorAll('.suggestion-item');
        suggestionItems.forEach(item => {
            item.addEventListener('click', function() {
                const bookId = this.getAttribute('data-book-id');
                if (bookId) {
                    // Chuyển đến trang chi tiết sách
                    window.location.href = `/books/${bookId}`;
                } else {
                    // Fallback: submit form với title
                    const text = this.getAttribute('data-text');
                    searchInput.value = text;
                    hideSuggestions();
                    searchForm.submit();
                }
            });
        });
    }

    // Highlight matching text
    function highlightMatch(text, query) {
        if (!query) return text;
        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<strong>$1</strong>');
    }

    // Hide suggestions
    function hideSuggestions() {
        searchSuggestions.classList.remove('show');
        searchSuggestions.innerHTML = '';
        currentSuggestionIndex = -1;
    }

    // Handle keyboard navigation
    function handleKeyNavigation(e) {
        const suggestionItems = searchSuggestions.querySelectorAll('.suggestion-item');

        if (suggestionItems.length === 0) return;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentSuggestionIndex = Math.min(currentSuggestionIndex + 1, suggestionItems.length - 1);
                updateActiveItem(suggestionItems);
                break;

            case 'ArrowUp':
                e.preventDefault();
                currentSuggestionIndex = Math.max(currentSuggestionIndex - 1, -1);
                updateActiveItem(suggestionItems);
                break;

            case 'Enter':
                if (currentSuggestionIndex >= 0) {
                    e.preventDefault();
                    const activeItem = suggestionItems[currentSuggestionIndex];
                    const bookId = activeItem.getAttribute('data-book-id');
                    if (bookId) {
                        // Chuyển đến trang chi tiết sách
                        window.location.href = `/books/${bookId}`;
                    } else {
                        // Fallback: submit form với title
                        const text = activeItem.getAttribute('data-text');
                        searchInput.value = text;
                        hideSuggestions();
                        searchForm.submit();
                    }
                }
                break;

            case 'Escape':
                hideSuggestions();
                searchInput.blur();
                break;
        }
    }

    // Update active suggestion item
    function updateActiveItem(items) {
        items.forEach((item, index) => {
            if (index === currentSuggestionIndex) {
                item.classList.add('active');
                item.scrollIntoView({ block: 'nearest' });
            } else {
                item.classList.remove('active');
            }
        });
    }

    // Event listeners
    searchInput.addEventListener('input', function(e) {
        const query = e.target.value.trim();
        console.log('Input event triggered, query:', query);
        debounceSearch(query);
    });

    searchInput.addEventListener('keydown', handleKeyNavigation);

    searchInput.addEventListener('focus', function() {
        const query = this.value.trim();
        if (query.length >= 1) {
            debounceSearch(query);
        }
    });

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
            hideSuggestions();
        }
    });

    // Handle form submission
    searchForm.addEventListener('submit', function(e) {
        const query = searchInput.value.trim();
        if (!query) {
            e.preventDefault();
            searchInput.focus();
            return;
        }
        hideSuggestions();
    });

    // Handle search input clear
    searchInput.addEventListener('keyup', function(e) {
        if (e.key === 'Backspace' || e.key === 'Delete') {
            if (this.value.length < 2) {
                hideSuggestions();
            }
        }
    });
});
