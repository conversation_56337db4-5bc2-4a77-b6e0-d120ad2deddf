// Tải các biến môi trường từ file .env
require('dotenv').config();

// Import các thư viện cần thiết
const express = require('express');
const cors = require('cors');
const path = require('path');
const cookieParser = require('cookie-parser');
const session = require('express-session');
const connectDB = require('./config/db');

// Import các routes
const authRoute = require('./routes/authRoute');
const bookRoute = require('./routes/bookRoute');
const searchRoute = require('./routes/searchRoute');
const cartRoute = require('./routes/cartRoute');
const userRoute = require('./routes/userRoute');
const chekoutRoute = require('./routes/checkoutRoute');
const orderRoute = require('./routes/orderRoute');
const reviewRoute = require('./routes/reviewRoute');
const adminRoute = require('./routes/adminRoute');

// Tạo ứng dụng Express
const app = express();

// Đặt cổng cho server
const PORT = process.env.PORT;

// Kết nối đến cơ sở dữ liệu MongoDB
connectDB();

// Cấu hình middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());
app.use(cookieParser());

// Cấu hình session
app.use(session({
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: true,
  cookie: { maxAge: 3600000 } // 1 giờ
}));

// Middleware để load user info cho tất cả routes
app.use(async function(req, res, next) {
  try {
    const User = require('./models/userModel');
    const jwt = require('jsonwebtoken');

    const token = req.cookies.token;
    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id).select("-password");
      if (user) {
        req.user = user;
        res.locals.user = user;
      }
    }
  } catch (error) {
    // Không làm gì, chỉ tiếp tục mà không có user
  }
  next();
});

// Middleware để xử lý thông báo từ query parameters
app.use(function(req, res, next) {
  res.locals.success = req.query.success || null;
  res.locals.error = req.query.error || null;
  next();
});

// Cấu hình view engine
app.set('view engine', 'pug');
app.set('views', path.join(__dirname, '../Frontend/views'));

// Cấu hình thư mục tĩnh
app.use('/public', express.static(path.join(__dirname, '../Frontend/public')));
app.use('/uploads', express.static(path.join(__dirname, '../Frontend/public/uploads')));

// Route cho trang chủ
app.get('/', function(req, res) {
  res.redirect('/books/home');
});

app.get('/login', function(req, res) {
  res.redirect('/auth/login');
});

app.get('/register', function(req, res) {
  res.redirect('/auth/register');
});

app.get('/test-search', function(req, res) {
  res.render('test-search');
});

app.get('/debug-search', function(req, res) {
  res.render('debug-search');
});

app.get('/test-api', async function(req, res) {
  try {
    const Book = require('./models/bookModel');
    const books = await Book.find().limit(5).lean();
    const totalBooks = await Book.countDocuments();
    res.json({
      message: 'API working',
      totalBooks: totalBooks,
      sampleBooks: books.map(book => ({
        id: book._id,
        title: book.title,
        author: book.author,
        price: book.price
      }))
    });
  } catch (error) {
    res.json({ error: error.message });
  }
});

app.use('/auth', authRoute);
app.use('/books', bookRoute);
app.use('/search', searchRoute);
app.use('/cart', cartRoute);
app.use('/checkout', chekoutRoute);
app.use('/orders', orderRoute);
app.use('/reviews', reviewRoute);
app.use('/', userRoute);
app.use('/admin', adminRoute);

app.use(function(err, req, res, next) {
  console.error(err.stack);
  res.status(500).json({ message: 'Lỗi server' });
});

app.listen(PORT, function() {
  console.log(`Server chạy tại http://localhost:${PORT}`);
});
