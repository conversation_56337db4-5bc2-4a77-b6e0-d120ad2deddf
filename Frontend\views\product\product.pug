doctype html
html(lang="vi")
    head
        title= book ? book.title : "Sản phẩm"
        meta(charset="UTF-8")
        meta(name="viewport", content="width=device-width, initial-scale=1.0")
        link(rel="stylesheet", href="/public/css/main.css")
        link(rel="stylesheet", href="/public/css/responsive.css")
        link(rel="stylesheet", href="/public/css/col.css")
        link(rel="stylesheet", href="/public/css/style.css")
        link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css")
    body
        include ../header.pug
        div#product-header
            //-Quay lại trang chủ
            div
                a(href="/")
                    button.fa-solid.fa-arrow-left
                p Sản phẩm
        div#product
            div.container
                div.product-left
                    div
                        img(src=book.image || "/public/img/default.jpg", alt=book.title)

                div.product-right
                    div.product-info
                        h1= book.title
                        div
                            i.fa-solid.fa-star
                                span= averageRating || '0'
                            p Đã bán
                                span= book.sold || 0
                        h3= `${book.price.toLocaleString('vi-VN')} đ`

                        // Hiển thị trạng thái tồn kho
                        div.stock-status
                            if book.stock <= 0
                                p.out-of-stock
                                    i.fa-solid.fa-exclamation-triangle
                                    span  Sản phẩm đã hết hàng
                            else if book.stock <= 5
                                p.low-stock
                                    i.fa-solid.fa-exclamation-circle
                                    span  Chỉ còn #{book.stock} sản phẩm
                            else
                                p.in-stock
                                    i.fa-solid.fa-check-circle
                                    span  Còn hàng (#{book.stock} sản phẩm)

                        if book.stock > 0
                            form#add-to-cart-form(data-book-id=book._id)
                                div.quantity
                                    label(for="quantity") Số lượng:
                                    div
                                        button.fa-solid.fa-plus#increase-qty(type="button")
                                        input#quantity(type="text", name="quantity", value="1", min="1", max=book.stock)
                                        button.fa-solid.fa-minus#decrease-qty(type="button")
                                div.prd-buy
                                    button#add-to-cart-btn(type="button")
                                        i.fa-solid.fa-cart-shopping
                                        p Thêm vào giỏ hàng
                                    button#buy-now-btn(type="button") Mua Ngay
                        else
                            div.out-of-stock-message
                                h4 Sản phẩm đã hết hàng
                    div.details-tb
                        h2 Thông tin chi tiết
                        table
                            tr
                                td Mã sách
                                td= book.code || 'Không có thông tin'
                            tr
                                td Tên nhà cung cấp
                                td= book.supplier || 'Không có thông tin'
                            tr
                                td Tác giả
                                td= book.author || 'Không có thông tin'
                            tr
                                td NXB
                                td= book.publisher || 'Không có thông tin'
                            tr
                                td Năm XB
                                td= book.year || 'Không có thông tin'
                            tr
                                td Ngôn Ngữ
                                td= book.language || 'Tiếng Việt'
                            tr
                                td Số trang
                                td= book.pageCount || 'Không có thông tin'
                            tr
                                td Hình thức
                                td= book.format || 'Không có thông tin'
                    div.product-desc
                        h2 Mô tả sản phẩm
                        h3= book.title
                        if book && book.description
                            p.desc.collapse= book.description
                        else
                            p.desc.collapse
                                | Chưa có mô tả cho sản phẩm này.
                        div
                            button.read-more-btn Xem thêm

        if book.stock > 0
            div#product-add
                form#mobile-add-to-cart-form(data-book-id=book._id)
                    div.quantity
                        button.fa-solid.fa-plus#mobile-increase-qty(type="button")
                        input#mobile-quantity(type="text", name="quantity", value="1", min="1", max=book.stock)
                        button.fa-solid.fa-minus#mobile-decrease-qty(type="button")
                    div.btn-add: button#mobile-add-to-cart-btn(type="button") Thêm vào giỏ hàng
                    div.btn-buy: button#mobile-buy-now-btn(type="button") Mua Ngay
        else
            div#product-add.out-of-stock-mobile
                p Sản phẩm đã hết hàng
        div.product-review#reviews
            div.container
                h2 Đánh giá sản phẩm
                div.review-rating
                    div
                        p= averageRating || '0.0'
                            span trên 5
                        div.star
                            - const fullStars = Math.floor(averageRating || 0)
                            - const hasHalfStar = (averageRating || 0) - fullStars >= 0.5
                            - for (let i = 1; i <= 5; i++)
                                if i <= fullStars
                                    i.fa-solid.fa-star
                                else if i === fullStars + 1 && hasHalfStar
                                    i.fa-solid.fa-star-half-stroke
                                else
                                    i.fa-regular.fa-star
                    ul
                        li.active: a Tất cả
                        li: a(href=`/books/${book._id}?rating=5`) 5 sao (#{reviews.filter(r => r.rating === 5).length})
                        li: a(href=`/books/${book._id}?rating=4`) 4 sao (#{reviews.filter(r => r.rating === 4).length})
                        li: a(href=`/books/${book._id}?rating=3`) 3 sao (#{reviews.filter(r => r.rating === 3).length})
                        li: a(href=`/books/${book._id}?rating=2`) 2 sao (#{reviews.filter(r => r.rating === 2).length})
                        li: a(href=`/books/${book._id}?rating=1`) 1 sao (#{reviews.filter(r => r.rating === 1).length})
                div.review-mess
                    if reviews && reviews.length > 0
                        each review in reviews
                            - const isUserReview = userReview && review._id.toString() === userReview._id.toString()
                            div.mess(class=isUserReview ? 'user-review' : '', style=isUserReview ? 'border: 2px solid #007bff; background-color: #f8f9fa;' : '')
                                h5= review.user ? review.user.username : 'Người dùng ẩn danh'
                                    if isUserReview
                                        span.badge(style="margin-left: 10px; background-color: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;") Đánh giá của bạn
                                div.star-mess
                                    - for (let i = 1; i <= 5; i++)
                                        if i <= review.rating
                                            i.fa-solid.fa-star
                                        else
                                            i.fa-regular.fa-star
                                if review.title
                                    h4(style="margin: 10px 0; font-weight: bold;")= review.title
                                p= review.comment
                    else
                        div.mess
                            p.text-center Chưa có đánh giá nào cho sản phẩm này.
                div.listpage
                    ul
                        li: a(href=`/books/${book._id}?page=${page > 1 ? page - 1 : 1}`): i.fa-solid.fa-chevron-left
                        li.active: a(href=`/books/${book._id}?page=1`) 1
                        li: a(href=`/books/${book._id}?page=2`) 2
                        li: a(href=`/books/${book._id}?page=3`) 3
                        li: a(href="#") ...
                        li: a(href=`/books/${book._id}?page=${page < Math.ceil(totalReviews / 5) ? page + 1 : Math.ceil(totalReviews / 5)}`): i.fa-solid.fa-chevron-right
        include ../footer.pug
    script(src='/public/js/search.js')
    script.
      document.addEventListener("DOMContentLoaded", () => {
        // Kiểm tra nếu URL có anchor #reviews thì scroll đến phần đánh giá
        if (window.location.hash === '#reviews') {
          const reviewsSection = document.getElementById('reviews');
          if (reviewsSection) {
            reviewsSection.scrollIntoView({ behavior: 'smooth' });
          }
        }

        //XEM THÊM
        const desc = document.querySelector('.desc');
        const btn = document.querySelector('.read-more-btn');

        if (btn && desc) {
          btn.addEventListener("click", function(){
            if (desc.classList.contains('collapse')) {
              desc.classList.remove('collapse');
              desc.classList.add('expanded');
              btn.textContent = 'Thu gọn';
            } else {
              desc.classList.remove('expanded');
              desc.classList.add('collapse');
              btn.textContent = 'Xem thêm';
            }
          });
        }

        // Xử lý số lượng sản phẩm
        const quantityInput = document.getElementById('quantity');
        const mobileQuantityInput = document.getElementById('mobile-quantity');
        const increaseBtn = document.getElementById('increase-qty');
        const decreaseBtn = document.getElementById('decrease-qty');
        const mobileIncreaseBtn = document.getElementById('mobile-increase-qty');
        const mobileDecreaseBtn = document.getElementById('mobile-decrease-qty');

        function updateQuantity(input, change) {
          let value = parseInt(input.value) || 1;
          const maxStock = parseInt(input.getAttribute('max')) || 999;

          value += change;
          if (value < 1) value = 1;
          if (value > maxStock) value = maxStock;

          input.value = value;
        }

        if (increaseBtn) {
          increaseBtn.addEventListener('click', function() {
            updateQuantity(quantityInput, 1);
          });
        }

        if (decreaseBtn) {
          decreaseBtn.addEventListener('click', function() {
            updateQuantity(quantityInput, -1);
          });
        }

        if (mobileIncreaseBtn) {
          mobileIncreaseBtn.addEventListener('click', function() {
            updateQuantity(mobileQuantityInput, 1);
          });
        }

        if (mobileDecreaseBtn) {
          mobileDecreaseBtn.addEventListener('click', function() {
            updateQuantity(mobileQuantityInput, -1);
          });
        }

        // Xử lý thêm vào giỏ hàng
        const addToCartForm = document.getElementById('add-to-cart-form');
        const mobileAddToCartForm = document.getElementById('mobile-add-to-cart-form');
        const addToCartBtn = document.getElementById('add-to-cart-btn');
        const mobileAddToCartBtn = document.getElementById('mobile-add-to-cart-btn');
        const buyNowBtn = document.getElementById('buy-now-btn');
        const mobileBuyNowBtn = document.getElementById('mobile-buy-now-btn');

        async function addToCart(form, redirect = false) {
          if (!form) {
            alert('Lỗi: Form không tồn tại');
            return;
          }

          const bookId = form.dataset.bookId;
          if (!bookId) {
            alert('Không thể thêm sản phẩm vào giỏ hàng');
            return;
          }

          const quantity = form.querySelector('input[name="quantity"]').value || 1;

          try {
            const response = await fetch(`/cart/add/${bookId}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ quantity })
            });

            if (response.ok) {
              if (redirect) {
                window.location.href = '/cart';
              } else {
                alert('Đã thêm sản phẩm vào giỏ hàng');
              }
            } else {
              // Xử lý các loại lỗi khác nhau
              const errorData = await response.json();

              if (errorData.outOfStock) {
                alert('Sản phẩm đã hết hàng!');
              } else if (errorData.availableStock) {
                alert(`Chỉ còn ${errorData.availableStock} sản phẩm trong kho!`);
              } else if (errorData.lastItem) {
                alert('Sản phẩm này chỉ còn 1 và bạn đã có trong giỏ hàng!');
              } else {
                alert(errorData.message || 'Không thể thêm sản phẩm vào giỏ hàng');
              }
            }
          } catch (error) {
            console.error('Error:', error);
            alert('Đã xảy ra lỗi khi thêm sản phẩm vào giỏ hàng');
          }
        }

        if (addToCartBtn) {
          addToCartBtn.addEventListener('click', function() {
            addToCart(addToCartForm);
          });
        }

        if (mobileAddToCartBtn) {
          mobileAddToCartBtn.addEventListener('click', function() {
            addToCart(mobileAddToCartForm);
          });
        }

        if (buyNowBtn) {
          buyNowBtn.addEventListener('click', function() {
            addToCart(addToCartForm, true);
          });
        }

        if (mobileBuyNowBtn) {
          mobileBuyNowBtn.addEventListener('click', function() {
            addToCart(mobileAddToCartForm, true);
          });
        }
      });
