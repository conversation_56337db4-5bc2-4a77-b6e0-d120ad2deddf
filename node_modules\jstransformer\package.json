{"name": "jstransformer", "version": "1.0.0", "description": "Normalize the API of any jstransformer", "keywords": ["jstransformer"], "dependencies": {"is-promise": "^2.0.0", "promise": "^7.0.1"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.4.0", "testit": "^2.0.2"}, "scripts": {"test": "node test", "coverage": "istanbul cover test", "coveralls": "npm run coverage && cat ./coverage/lcov.info | coveralls"}, "files": ["index.js"], "repository": {"type": "git", "url": "https://github.com/jstransformers/jstransformer.git"}, "author": "ForbesLindesay", "license": "MIT"}