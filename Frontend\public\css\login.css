body {
    background: #e6f0ff;
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  }
  
  .container {
    max-width: 400px;
    margin: 80px auto;
    background: #fff;
    padding: 30px 40px;
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
  }
  
  h2 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
  }
  
  input[type="text"],
  input[type="password"] {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    font-size: 15px;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
  
  button {
    width: 100%;
    padding: 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
  }
  
  button:hover {
    background-color: #0056b3;
  }
  
  p {
    text-align: center;
    margin-top: 15px;
  }
  
  p a {
    color: #007bff;
    text-decoration: none;
  }
  
  p.error {
    color: red;
    margin-bottom: 10px;
    text-align: center;
  }
  