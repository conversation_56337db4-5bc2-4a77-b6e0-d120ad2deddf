const User = require('../models/userModel');

const getUserProfile = async function(req, res, returnOnly = false) {
  try {
    console.log('getUserProfile được gọi với userId:', req.user._id);

    const user = await User.findById(req.user._id).select('-password');
    console.log('Kết quả truy vấn user:', user ? 'Tìm thấy user' : '<PERSON>hông tìm thấy user');

    if (!user) {
      console.log('Không tìm thấy user với id:', req.user._id);
      if (returnOnly) return null;
      return res.status(404).json({ message: '<PERSON>hông tìm thấy người dùng' });
    }

    if (returnOnly) return user;
    res.json(user);
  } catch (error) {
    console.log('Lỗi trong getUserProfile:', error);
    if (returnOnly) throw error;
    res.status(500).json({ message: 'Lỗi server' });
  }
};

const updateUser = async function(req, res) {
  try {
    const { fullname, phone, email, birthday } = req.body;
    console.log('Dữ liệu form cập nhật thông tin:', req.body);

    const user = await User.findById(req.user._id);
    if (!user) return false;

    user.fullname = fullname;
    user.phone = phone;
    user.email = email;
    user.birthday = birthday;

    await user.save();
    console.log('Đã lưu thông tin người dùng:', {
      fullname: user.fullname,
      phone: user.phone,
      email: user.email,
      birthday: user.birthday
    });
    return true;
  } catch (error) {
    console.log(error);
    return false;
  }
};

const updateAddress = async function(req, res) {
  try {
    if (!req.user) {
      console.log('Không có thông tin người dùng trong request');
      return res.redirect('/auth/login');
    }

    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ error: 'Không tìm thấy thông tin người dùng' });
    }

    console.log('Dữ liệu form địa chỉ:', req.body);

    user.address = {
      fullname: req.body.name,
      phone: req.body.phone,
      country: req.body.country || 'Việt Nam',
      city: req.body.city,
      district: req.body.district,
      ward: req.body.ward,
      address: req.body.address,
      type: req.body.addressType || 'home'
    };

    await user.save();
    console.log('Đã lưu địa chỉ:', user.address);
    return res.redirect('/customer/address');
  } catch (err) {
    console.error('Lỗi cập nhật địa chỉ:', err.message, err.stack);

    const userData = {
      _id: req.user._id,
      username: req.user.username,
      fullname: req.user.fullname || '',
      phone: req.user.phone || '',
      email: req.user.email || '',
      birthday: req.user.birthday ? (typeof req.user.birthday === 'string' ? req.user.birthday : req.user.birthday.toISOString().split('T')[0]) : '',
      address: req.user.address || {}
    };

    return res.status(500).render('customer/customer-addr', {
      user: userData,
      error: 'Lỗi cập nhật địa chỉ'
    });
  }
};



module.exports = {
  getUserProfile,
  updateUser,
  updateAddress
};
