doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title Tài khoản - WEB BÁN SÁCH TRUYỆN NHÓM 9
    link(rel="stylesheet", href="/public/css/main.css")
    link(rel="stylesheet", href="/public/css/style.css")
    link(rel="stylesheet", href="/public/css/responsive.css")
    link(rel="stylesheet", href="/public/css/col.css")
    script(src='/public/js/logout.js')
    link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css")
  body
    include ../header.pug

    div(id="customer")
      div(class="customer-left")
        div
          div
            //-Thay thẻ a thành thẻ button
            ul
              li(class="active"): a(href="/customer") Thông tin tài khoản
              li: a(href="/customer/address") Thông tin địa chỉ
              li: a(href="/customer/change-password") Đổi mật khẩu
              li: a(href="/customer/orders") Đơn hàng của bạn
            button.log-out Đăng xuất
      div(class="customer-right")
        h1 Thông tin tài khoản
        div
          if error
            div(class="alert-error")= error
          form(class="customer-form", action="/customer/update", method="POST")
            div(class="form-info")
              p Họ và tên
              input(type="text", name="fullname", placeholder="Họ và tên", value=user ? user.fullname || "" : "", required)
            div(class="form-info")
              p Số điện thoại
              input(type="text", name="phone", placeholder="Số điện thoại", value=user ? user.phone || "" : "", required)
            div(class="form-info")
              p Email
              input(type="text", name="email", placeholder="Email", value=user ? user.email || "" : "", required)
            div(class="form-info")
              p Ngày sinh
              input(type="date", name="birthday", placeholder="Ngày sinh", value=user && user.birthday ? (typeof user.birthday === 'string' ? user.birthday : user.birthday.toISOString().split('T')[0]) : "")
            div(class="form-info")
              button(class="create-update", type="submit") Cập nhật

    include ../logout.pug
    include ../footer.pug
    script(src='/public/js/search.js')