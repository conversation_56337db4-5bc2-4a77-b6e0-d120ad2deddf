doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title ADMIN - Quản lí khách hàng
    link(rel='stylesheet' href='/public/css/admin.css')
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css')
  body
    header
      .content
        ul.logo
          li
            h3 WEB BÁN SÁCH TRUYỆN NHÓM 9 - ADMIN
          li
            a(href='/auth/logout')
              | Đăng xuất
              i.fa-solid.fa-right-from-bracket
        ul.quanli
          li
            a(href='/admin')
              i.fa-solid.fa-house
              h4 Quản lí thông tin tổng quát
          li
            a(href='/admin/user')
              i.fa-solid.fa-circle-user
              h4 Quản lí người dùng
          li
            a(href='/admin/book')
              i.fa-solid.fa-book
              h4 Quản lí sách
          li
            a(href='/admin/category')
              i.fa-solid.fa-address-book
              h4 Quản lí danh mục
          li
            a(href='/admin/order')
              i.fa-solid.fa-cart-shopping
              h4 Quản lí đơn hàng
          li
            a(href='/admin/sale')
              i.fa-solid.fa-sack-dollar
              h4 Quản lí doanh thu
          li
            a(href='/')
              i.fa-solid.fa-share-from-square
              h4 Quay về trang chủ

    .main
      .admin
        h2 Quản lí thông tin khách hàng

    .danhsach
      .danhsachthongtin
        h3 QUẢN LÍ THÔNG TIN KHÁCH HÀNG
        table.thongtin
          tr
            th ID
            th Tên
            th Số điện thoại
            th Email
            th Quyền
            th Ngày tạo
            th Thao tác
          if users && users.length > 0
            each user in users
              tr
                td= user._id
                td= user.username
                td= user.phone || 'Chưa cập nhật'
                td= user.email
                td= user.role === 'admin' ? 'Admin' : 'Người dùng'
                td= user.createdAt ? new Date(user.createdAt).toLocaleDateString('vi-VN') : 'N/A'
                td
                  button.btn2(onclick=`editUser('${user._id}')`)
                    i.fa-solid.fa-pen
                  button.btn3(onclick=`deleteUser('${user._id}')`)
                    i.fa-solid.fa-trash-can
          else
            tr
              td(colspan="7" style="text-align: center; padding: 20px;") Không có dữ liệu người dùng
    script(src='/public/js/admin.js')
