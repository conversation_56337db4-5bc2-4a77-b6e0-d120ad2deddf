{"name": "character-parser", "version": "2.2.0", "description": "Parse JavaScript one character at a time to look for snippets in Templates.  This is not a validator, it's just designed to allow you to have sections of JavaScript delimited by brackets robustly.", "main": "index.js", "scripts": {"coverage": "istanbul cover test/index.js", "test": "node test/index.js"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/character-parser.git"}, "keywords": ["parser", "JavaScript", "bracket", "nesting", "comment", "string", "escape", "escaping"], "author": "ForbesLindesay", "license": "MIT", "devDependencies": {"istanbul": "~0.3.22", "testit": "~2.0.2"}, "dependencies": {"is-regex": "^1.0.3"}}