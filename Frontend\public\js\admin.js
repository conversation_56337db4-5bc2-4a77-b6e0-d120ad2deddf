// Admin JavaScript Functions

// User Management Functions
function editUser(userId) {
    // Redirect to edit user page or open modal
    window.location.href = `/admin/user/edit/${userId}`;
}

function deleteUser(userId) {
    if (confirm('Bạn có chắc chắn muốn xóa người dùng này?')) {
        fetch(`/admin/user/delete/${userId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Xóa người dùng thành công!');
                location.reload();
            } else {
                alert('Có lỗi xảy ra: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi xóa người dùng');
        });
    }
}

// Book Management Functions
function editBook(bookId) {
    window.location.href = `/admin/book/edit/${bookId}`;
}

function deleteBook(bookId) {
    if (confirm('Bạn có chắc chắn muốn xóa sách này?')) {
        fetch(`/admin/book/delete/${bookId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Xóa sách thành công!');
                location.reload();
            } else {
                alert('Có lỗi xảy ra: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi xóa sách');
        });
    }
}

// Order Management Functions
function viewOrder(orderId) {
    window.location.href = `/admin/order/view/${orderId}`;
}

function updateOrderStatus(orderId, status) {
    fetch(`/admin/order/update-status/${orderId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Cập nhật trạng thái đơn hàng thành công!');
            location.reload();
        } else {
            alert('Có lỗi xảy ra: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi cập nhật trạng thái');
    });
}

// Category Management Functions
function editCategory(categoryId) {
    window.location.href = `/admin/category/edit/${categoryId}`;
}

function deleteCategory(categoryId) {
    if (confirm('Bạn có chắc chắn muốn xóa danh mục này?')) {
        fetch(`/admin/category/delete/${categoryId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Xóa danh mục thành công!');
                location.reload();
            } else {
                alert('Có lỗi xảy ra: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi xóa danh mục');
        });
    }
}

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    
    for (let input of inputs) {
        if (!input.value.trim()) {
            alert(`Vui lòng điền ${input.getAttribute('placeholder') || input.name}`);
            input.focus();
            return false;
        }
    }
    return true;
}

// Image preview
function previewImage(input, previewId) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById(previewId).src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Initialize admin functions when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Add any initialization code here
    console.log('Admin panel loaded');
});
