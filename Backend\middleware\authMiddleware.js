const User = require("../models/userModel");
const jwt = require("jsonwebtoken");

const auth = async (req, res, next) => {
    try {
        const token = req.cookies.token;

        if (!token) {
            console.log('Không tìm thấy token');
            res.locals.user = null;
            return res.redirect('/auth/login?message=Vui lòng đăng nhập để tiếp tục');
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id).select("-password");

        if (!user) {
            console.log('Không tìm thấy user');
            res.locals.user = null;
            return res.redirect('/auth/login?message=Tài khoản không tồn tại');
        }

        console.log('User đã đăng nhập:', user.username, 'Role:', user.role);
        req.user = user;
        res.locals.user = user;
        next();
    } catch (error) {
        console.log('Lỗi x<PERSON>c thực:', error.message);
        res.locals.user = null;
        return res.redirect('/auth/login?message=Phiên đăng nhập hết hạn, vui lòng đăng nhập lại');
    }
};

const isAdmin = (req, res, next) => {
    if (!req.user || req.user.role !== "admin") {
        return res.redirect('/auth/login?message=Bạn cần quyền admin để truy cập trang này');
    }
    next();
};

// Middleware để load user cho tất cả routes (không redirect nếu chưa đăng nhập)
const loadUser = async (req, res, next) => {
    try {
        const token = req.cookies.token;

        if (!token) {
            // Chưa đăng nhập - không redirect, chỉ set user = null
            req.user = null;
            res.locals.user = null;
            return next();
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id).select("-password");

        if (!user) {
            // User không tồn tại - clear cookie và set user = null
            res.clearCookie('token');
            req.user = null;
            res.locals.user = null;
            return next();
        }

        // User hợp lệ - set vào req và res.locals
        req.user = user;
        res.locals.user = user;
        next();
    } catch (error) {
        // Token không hợp lệ - clear cookie và set user = null
        res.clearCookie('token');
        req.user = null;
        res.locals.user = null;
        next();
    }
};

module.exports = { auth, isAdmin, loadUser };
