// <PERSON><PERSON><PERSON> b<PERSON><PERSON> đã load hoàn toàn trước khi chạy JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Home.js loaded successfully!'); // Debug log

    const slideContainer = document.getElementById("slide-container");
    const nextBtn = document.getElementById("nextBtn");
    const prevBtn = document.getElementById("prevBtn");

    // Kiểm tra xem các element có tồn tại không
    if (!slideContainer) {
        console.error('Không tìm thấy slide-container');
        return;
    }

    if (!nextBtn || !prevBtn) {
        console.error('Không tìm thấy nút next hoặc prev');
        return;
    }

    const totalSlides = slideContainer.querySelectorAll("img").length;
    let currentIndex = 0;

    console.log('Tổng số slide:', totalSlides); // Debug log

    function showSlide(index) {
        const offset = index * -1220; // mỗi ảnh rộng 1220px
        slideContainer.style.transform = `translateX(${offset}px)`;
        console.log('Chuyển đến slide:', index); // Debug log
    }

    function nextSlide() {
        currentIndex = (currentIndex + 1) % totalSlides;
        showSlide(currentIndex);
    }

    function prevSlide() {
        currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
        showSlide(currentIndex);
    }

    // Nút bấm
    nextBtn.addEventListener("click", nextSlide);
    prevBtn.addEventListener("click", prevSlide);

    // Chạy slide tự động mỗi 3 giây
    setInterval(nextSlide, 3000);

    console.log('Slider đã được khởi tạo thành công!'); // Debug log
});