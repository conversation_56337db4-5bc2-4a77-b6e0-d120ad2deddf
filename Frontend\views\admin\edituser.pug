doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title ADMIN - Chỉnh sửa người dùng
    link(rel='stylesheet' href='/public/css/admin.css')
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css')
  body
    header
      .content
        ul.logo
          li
            h3 WEB BÁN SÁCH TRUYỆN NHÓM 9 - ADMIN
          li
            a(href='/auth/logout')
              | Đăng xuất
              i.fa-solid.fa-right-from-bracket
        ul.quanli
          li
            a(href='/admin')
              i.fa-solid.fa-house
              h4 Quản lí thông tin tổng quát
          li
            a(href='/admin/user')
              i.fa-solid.fa-circle-user
              h4 Quản lí người dùng
          li
            a(href='/admin/book')
              i.fa-solid.fa-book
              h4 Quản lí sách
          li
            a(href='/admin/category')
              i.fa-solid.fa-address-book
              h4 Quản lí danh mục
          li
            a(href='/admin/order')
              i.fa-solid.fa-cart-shopping
              h4 Quản lí đơn hàng
          li
            a(href='/admin/sale')
              i.fa-solid.fa-sack-dollar
              h4 Quản lí doanh thu
          li
            a(href='/')
              i.fa-solid.fa-share-from-square
              h4 Quay về trang chủ
    .main
      .admin
        h2 Chỉnh sửa người dùng
    
    .capnhatuser
      if error
        .error-message
          p= error
      form(action=`/admin/user/edit/${user._id}` method="POST")
        .form-group
          h2 Chỉnh sửa thông tin người dùng
          .input-group
            label(for='username') Tên đăng nhập:
            input#username(type='text' name='username' value=user.username placeholder='Nhập tên đăng nhập' required)
          .input-group
            label(for='email') Email:
            input#email(type='email' name='email' value=user.email placeholder='Nhập email' required)
          .input-group
            label(for='fullname') Họ và tên:
            input#fullname(type='text' name='fullname' value=user.fullname || '' placeholder='Nhập họ và tên')
          .input-group
            label(for='phone') Số điện thoại:
            input#phone(type='text' name='phone' value=user.phone || '' placeholder='Nhập số điện thoại')
          .input-group
            label(for='role') Quyền:
            select#role(name='role' required)
              option(value='user' selected=(user.role === 'user')) Người dùng
              option(value='admin' selected=(user.role === 'admin')) Admin
          .input-group
            label ID người dùng:
            input(type='text' value=user._id readonly style='background-color: #f5f5f5; cursor: not-allowed;')
          .input-group
            label Ngày tạo:
            input(type='text' value=user.createdAt ? new Date(user.createdAt).toLocaleDateString('vi-VN') : 'N/A' readonly style='background-color: #f5f5f5; cursor: not-allowed;')
          .button-group
            button.btn-cancel(type='button' onclick="window.location.href='/admin/user'") Hủy
            button.btn-save(type='submit') Cập nhật
    script(src='/public/js/admin.js')
