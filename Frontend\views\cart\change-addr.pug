html
    head
        title Địa chỉ của tôi
        link(rel="stylesheet", href="./public/css/main.css")
        link(rel="stylesheet", href="./public/css/responsive.css")
        script(src="./public/js/addrForm.js") 
        link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css")
    body(class="change-address")
        div(class="change-address-head")
            div
                //-Quay lại trang checkout
                button(class="fa-solid fa-arrow-left")
                P Địa chỉ của tôi
        div(class="change-address-body")
            div
                form
                    div(class="form-info")
                        p Họ và tên                   
                        input(type="text" name="name" placeholder="Họ và tên" value="" required)
                    div(class="form-info")
                        p <PERSON>ố điện thoại
                        input(type="text" name="phone" placeholder="Số điện thoại" value="" required)
                    div(class="form-info")
                        label( for="province") Tỉnh/Thành phố
                        select( id="province"  required)
                            option( value="") Chọn Tỉnh/Thành phố
                    div(class="form-info")
                        label( for="district") Quận/Huyện
                        select( id="district"  required)
                            option( value="") Chọn Quận/Huyện
                    div(class="form-info")
                        label( for="ward") Xã/Phường
                        select( id="ward"  required)
                            option( value="") Chọn Xã/Phường
                    div(class="form-info")
                        p Địa chỉ
                        input(type="text" name="address" placeholder="Địa chỉ" value="" required)
                    div(class="form-info")
                        p Loại địa chỉ
                        div(class="radio-group")
                            div
                                input( type="radio" id="home" name="type" checked)
                                label(for="home")  Nhà riêng / Chung cư 
                            div
                                input( type="radio" name="type")
                                label(for="")  Cơ quan / Công ty
                    div(class="form-info")
                        div(class="btn-group")
                            //- Ấn xong một trong 2 nút sẽ tự đông quay lại trang checkout                    
                            button(class="cancel") Hủy
                            button(class="create-update") Cập nhật