doctype html
html(lang="vi")
  head
    title T<PERSON><PERSON> kho<PERSON>n
    meta(charset="UTF-8")
    meta(name="viewport", content="width=device-width, initial-scale=1.0")
    link(rel="stylesheet", href="/public/css/main.css")
    link(rel="stylesheet", href="/public/css/style.css")
    link(rel="stylesheet", href="/public/css/responsive.css")
    link(rel="stylesheet", href="/public/css/col.css")
    script(src='/public/js/logout.js')
    link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css")
  body
    include ../../header.pug

    div#customer-header
      //-Quay lại trang chủ
      div
        a(href="/customer")
          button.fa-solid.fa-arrow-left
        p <PERSON>à<PERSON> khoản
    div#customer
      div.customer-left
        div
          div
            //-Thay thẻ a thanh thẻ button
            ul
              li: a(href="/customer") Thông tin tài khoản
              li: a(href="/customer/address") Thông tin địa chỉ
              li: a(href="/customer/change-password") Đổi mật khẩu
              li.active: a(href="/customer/orders") Đơn hàng của bạn
            button.log-out Đăng xuất
      div.customer-right
        h1 Đơn hàng của bạn
        div
          if error
            div.alert.alert-danger= error
          div.order
            div.prd-order
              ul
                li(class=!currentStatus || currentStatus === 'all' ? 'active' : ''): a(href="/customer/orders") Tất cả
                li(class=currentStatus === 'pending' ? 'active' : ''): a(href="/customer/orders?status=pending") Chờ xác nhận
                li(class=currentStatus === 'transporting' ? 'active' : ''): a(href="/customer/orders?status=transporting") Chờ giao hàng
                li(class=currentStatus === 'delivered' ? 'active' : ''): a(href="/customer/orders?status=delivered") Đã giao
                li(class=currentStatus === 'cancelled' ? 'active' : ''): a(href="/customer/orders?status=cancelled") Đã hủy

          //-Không tồn tại đơn nào (dùng code này khi không có đơn hàng nào)
          if !orders || orders.length === 0
            div.empty-order
              i.fa-solid.fa-bag-shopping
              p Bạn chưa có đơn nào
          else
            div
              each order in orders
                div.prd
                  div
                    div
                      if order.status === 'delivered'
                        i.fa-solid.fa-truck
                        p Giao hàng thành công
                        div Hoàn thành
                      else if order.status === 'transporting'
                        i.fa-solid.fa-truck
                        p Đang giao hàng
                        div Đang giao
                      else if order.status === 'pending'
                        i.fa-solid.fa-clock
                        p Chờ xác nhận
                        div Chờ xác nhận
                      else if order.status === 'cancelled'
                        i.fa-solid.fa-times
                        p Đã hủy
                        div Đã hủy
                  div
                    each item in order.items
                      if item.book
                        img(src=item.book.image || '/public/img/default-book.jpg', alt=item.book.title)
                        div.prd-info
                          h2= item.book.title
                          div
                            p x#{item.quantity}
                            p= `${item.price.toLocaleString('vi-VN')} đ`
                      else
                        img(src='/public/img/default-book.jpg', alt='Sản phẩm không tồn tại')
                        div.prd-info
                          h2 Sản phẩm không tồn tại
                          div
                            p x#{item.quantity}
                            p= `${item.price.toLocaleString('vi-VN')} đ`
                  div
                    p Tổng tiền:
                      span= `${order.totalPrice.toLocaleString('vi-VN')} đ`
                    if order.status === 'delivered'
                      // Luôn hiển thị nút đánh giá cho đơn hàng đã giao
                      - const firstItem = order.items.find(item => item.book)
                      if firstItem
                        button.btn-review(data-order-id=order._id) Đánh giá
                    if order.status === 'pending'
                      button.btn-cancel(data-order-id=order._id) Hủy đơn hàng
                    if order.status === 'delivered' || order.status === 'cancelled'
                      button.btn-buy(data-order-id=order._id) Mua lại

            if pagination && pagination.pages > 1
              div.listpage
                ul
                  if pagination.page > 1
                    li: a(href=`/customer/orders?page=${pagination.page - 1}${currentStatus && currentStatus !== 'all' ? `&status=${currentStatus}` : ''}`): i.fa-solid.fa-chevron-left

                  - for (let i = 1; i <= pagination.pages; i++)
                    li(class=i === pagination.page ? 'active' : ''): a(href=`/customer/orders?page=${i}${currentStatus && currentStatus !== 'all' ? `&status=${currentStatus}` : ''}`)= i

                  if pagination.page < pagination.pages
                    li: a(href=`/customer/orders?page=${pagination.page + 1}${currentStatus && currentStatus !== 'all' ? `&status=${currentStatus}` : ''}`): i.fa-solid.fa-chevron-right

    //-Form đánh giá
    div.review
      form(action="/reviews/add", method="POST")
        input(type="hidden", name="orderItemId", id="orderItemId")
        div
          input.star.star-5#star-5(type="radio", name="rating", value="5")
          label.star.star-5(for="star-5")
          input.star.star-4#star-4(type="radio", name="rating", value="4")
          label.star.star-4(for="star-4")
          input.star.star-3#star-3(type="radio", name="rating", value="3")
          label.star.star-3(for="star-3")
          input.star.star-2#star-2(type="radio", name="rating", value="2")
          label.star.star-2(for="star-2")
          input.star.star-1#star-1(type="radio", name="rating", value="1")
          label.star.star-1(for="star-1")

        textarea.form-note(type="text", name="content", required, placeholder="Nhập nhận xét của bạn")
        div.btn
          button.cancel(type="button") Hủy
          button.review-mess(type="submit") Gửi đánh giá

    include ../../logout.pug
    include ../../footer.pug

    script.
      document.addEventListener('DOMContentLoaded', function() {
        const reviewBtn = document.querySelectorAll(".btn-review");
        const review = document.querySelector(".review");
        const cancelBtn = document.querySelector(".cancel");
        const messBtn = document.querySelector(".review-mess");

        reviewBtn.forEach(btn => {
          btn.addEventListener("click", function () {
            const orderId = this.getAttribute('data-order-id');
            document.getElementById('orderItemId').value = orderId;
            review.classList.add("open");
          });
        });

        cancelBtn.addEventListener("click", function () {
          review.classList.remove("open");
        });

        // Handle view review button clicks
        const viewReviewButtons = document.querySelectorAll('.btn-view-review');

        viewReviewButtons.forEach(button => {
          button.addEventListener('click', function() {
            const bookId = this.getAttribute('data-book-id');
            // Chuyển hướng đến trang sản phẩm với anchor đến phần đánh giá
            window.location.href = `/books/${bookId}#reviews`;
          });
        });

        // Handle buy again button clicks
        const buyAgainButtons = document.querySelectorAll('.btn-buy');

        buyAgainButtons.forEach(button => {
          button.addEventListener('click', async function() {
            try {
              const orderId = this.getAttribute('data-order-id');

              const response = await fetch(`/orders/${orderId}/buy-again`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                }
              });

              if (response.ok) {
                window.location.href = '/cart';
              } else {
                alert('Không thể thêm sản phẩm vào giỏ hàng');
              }
            } catch (error) {
              console.error('Error:', error);
              alert('Đã xảy ra lỗi khi thêm sản phẩm vào giỏ hàng');
            }
          });
        });

        // Xử lý nút hủy đơn hàng
        document.addEventListener('click', function(e) {
          if (e.target.classList.contains('btn-cancel')) {
            const orderId = e.target.getAttribute('data-order-id');
            if (confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')) {
              // Gửi request hủy đơn hàng
              fetch(`/customer/orders/${orderId}/cancel`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                }
              })
              .then(response => response.json())
              .then(data => {
                if (data.success) {
                  alert('Đơn hàng đã được hủy thành công');
                  location.reload();
                } else {
                  alert(data.message || 'Có lỗi xảy ra khi hủy đơn hàng');
                }
              })
              .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra khi hủy đơn hàng');
              });
            }
          }
        });

        // Xử lý nút đăng xuất
        const logoutBtn = document.querySelector('.log-out');
        if (logoutBtn) {
          logoutBtn.addEventListener('click', function() {
            document.querySelector('.logout').classList.add('open');
          });
        }
      });

    script(src='/public/js/search.js')