*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, Helvetica, sans-serif;
}

header{

    font-family: Arial, Helvetica, sans-serif;
}

.content{
    background-color: #ffffff;
}

.content .logo{
    background-color: rgb(100, 100, 100);
    display: flex;
    justify-content: space-around;
    list-style-type: none;
    width: 100%;
    height: 60px;
    line-height: 60px;
}
.content .logo li:hover a{
    border-radius: 10px;
    background:linear-gradient(to right, #000000, #505050);
    color: #ffffff;

}

.content .logo li a{
    text-decoration: none;
    color: #000;
    padding: 21px;

}
.content .logo li i{
    margin: 0 10px;
}


.content .quanli{
    display: flex;
    flex-direction: column;
    list-style-type: none;
    background-color: rgb(226, 138, 74);
    width: 17%;
    position: absolute;
    height: 1000px;
    box-shadow: 0 0 8px  #000;



}

.content .quanli li{
    padding: 30px;
    margin: 10px 0;


}
.content .quanli li:hover a,
.content .quanli li:hover{
    color: #ffffff;
    background:linear-gradient(to right, #72d0ff, #87fbcb);
    border-radius: 8px;
}
.content .quanli li a{
    color: #000;
    text-decoration: none;
    text-align: center;


}
.content .quanli li a h4{
    font-size: 14px;

}
.content .quanli li a i{
    position: absolute;
    left: 10px;
}



/* Quản lí thông tin tổng quát */


.main{
    display: flex;
    justify-content: space-between;
    background-color: rgb(206, 206, 206);
    margin-left: 260px;
    padding: 20px;
    position: relative;

}
.main .admin h2{
    color: #3a3a3a;
}



/* Quản lí thông tin tổng quát */

.tongquat{
    margin-right: 10px;
    margin-left: 280px;

    background-color: beige;
    border-radius: 10px;
}
.tongquat .tongso{
    display: flex;
    justify-content: space-around;


    margin: 80px 50px;
}
.tongquat .tongso .tongso2{
    background:linear-gradient(to right, #96dcff, #4fffb6);
    padding: 20px 50px;
    margin: 20px 0;
    border-radius: 10px;
}
.tongquat .tongso .tongso2 i{
    border-radius: 100%;
    padding: 10px;
    color: #ffffff;
}
.tongquat .tongso .tongso2 .fa-book{
    background-color: rgb(10, 186, 250);
}
.tongquat .tongso .tongso2 .fa-sack-dollar{
    background-color: rgb(54, 243, 161);
}
.tongquat .tongso .tongso2 .fa-code-compare{
    background-color: rgb(235, 31, 31);
}
.tongquat .tongso .tongso2 .fa-bookmark{
    background-color: rgb(170, 231, 16);
}
.tongquat .tongso .tongso2 .fa-circle-user{
    background-color: rgb(100, 100, 100);
}
.tongquat .tongso .tongso2 .fa-warehouse{
    background-color: rgb(205, 29, 225);
}
.tongquat .tongso .tongso2 .fa-truck-ramp-box{
    background-color: rgb(171, 20, 83);
}




.capnhap{
    margin-left: 280px;
    display: flex;
    padding: 20px 30px;
    background-color: #72d0ff;
    width: 20%;
    border-radius: 10px;

}
.capnhap a{
    list-style-type: none;
    text-decoration: none;
    color: #0037ff;
}
.capnhap i{
    margin-right: 10px;
    color: #0037ff;
}
.capnhap .moi{
    display: none;
    list-style-type: none;
    position: absolute;
    width: 400px;
    height: 400px;
    background-color: #4fffb6;
    text-align: center;
    left: 500px;
    top: 188px;
}
.capnhap .moi li{
    margin: 30px 0;
}
.capnhap .moi li  input{
    width: 80%;
    height: 40px;
}
.capnhap .moi button{
    padding: 10px 30px;
    border-radius: 8px;
}
.capnhap .moi .luu{
    background-color: #365ef0;
}
.capnhap .moi .xoa{
    background-color: #fd5a24;
}
.capnhap:hover .moi{
    display: block;
}
.danhsach{
    margin-left: 280px;
    width: auto;
    margin-right: 20px;
    background-color: #d6d8d7;
    border-radius: 10px;

}

.danhsach .danhsachthongtin{
    text-align: center;




}
.danhsach .danhsachthongtin h3{
    background-color: #7c7a7a;
    padding: 30px 0;
    border-radius: 10px;

}

.danhsach .thongtin {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.danhsach .thongtin tr th{
    padding: 15px 10px;
    background-color: #365ef0;
    color: #ffffff;
    text-align: center;
    font-weight: bold;
    border: 1px solid #ddd;
}

.danhsach .thongtin tr td{
    padding: 12px 8px;
    text-align: center;
    border: 1px solid #ddd;
    background-color: #fff;
    word-wrap: break-word;
    max-width: 150px;
}

.danhsach .thongtin tr:nth-child(even) td {
    background-color: #f9f9f9;
}

.danhsach .thongtin tr:hover td {
    background-color: #f0f8ff;
}

.danhsach .thongtin tr td .btn2{
    padding: 8px 15px;
    background-color: #738ff7;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 5px;
    font-size: 12px;
}

.danhsach .thongtin tr td .btn2:hover{
    background-color: #5a7ae6;
}

.danhsach .thongtin tr td .btn3{
    padding: 8px 15px;
    background-color: #f56332;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.danhsach .thongtin tr td .btn3:hover{
    background-color: #e04e2a;
}

/* sach  */
.capnhapsach {
    margin-left: 280px;
    display: flex;
    padding: 20px 30px;
    background-color: #72d0ff;
    width: 15%;
    border-radius: 10px;
}
.capnhapsach a{
    list-style-type: none;
    text-decoration: none;
    color: #0037ff;
}
.capnhapsach i{
    margin-right: 10px;
    color: #0037ff;
}
/*.capnhapsach .sachmoi{
    display: none;
    list-style-type: none;
    position: absolute;
    width: 1150px;
    height: 660px;
    background-color: #4fffb6;
     text-align: center;
    left: 300px;
    top: 68px;
    border-radius: 10px;
}
.capnhapsach:hover .sachmoi {
    display: block;
}

.capnhapsach .sachmoi li{
    padding: 15px 0;
}
.capnhapsach .sachmoi li input{
    width: 40%;
    height: 30px;
}
.capnhapsach .sachmoi button{
    padding: 10px 60px ;
    border-radius: 10px;
}
.capnhapsach .sachmoi .luu{
    background-color: #0037ff;
}
.capnhapsach .sachmoi .xoa{
    background-color: #fd5a24;
}
 */
.capnhapsachmoi {
    margin-left: 280px;
    margin-top: 20px;
    margin-right: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    min-height: auto;
}

.capnhapsachmoi .form-container {
    padding: 30px;
}

.capnhapsachmoi .form-container h2 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 24px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.capnhapsachmoi .form-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.capnhapsachmoi .form-column {
    display: flex;
    flex-direction: column;
}

.capnhapsachmoi .form-group {
    margin-bottom: 20px;
}

.capnhapsachmoi .form-group.full-width {
    grid-column: span 2;
}

.capnhapsachmoi .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
    font-size: 14px;
}

.capnhapsachmoi .form-group label .optional {
    font-weight: normal;
    color: #666;
    font-size: 12px;
    font-style: italic;
}

.capnhapsachmoi .form-group input,
.capnhapsachmoi .form-group select,
.capnhapsachmoi .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.capnhapsachmoi .form-group input:focus,
.capnhapsachmoi .form-group select:focus,
.capnhapsachmoi .form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.capnhapsachmoi .form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.capnhapsachmoi .button-group {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.capnhapsachmoi .btn-cancel,
.capnhapsachmoi .btn-save {
    padding: 12px 30px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.capnhapsachmoi .btn-cancel {
    background-color: #6c757d;
    color: white;
}

.capnhapsachmoi .btn-cancel:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
}

.capnhapsachmoi .btn-save {
    background-color: #007bff;
    color: white;
}

.capnhapsachmoi .btn-save:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
}

/* Responsive design */
@media (max-width: 1200px) {
    .capnhapsachmoi {
        margin-left: 20px;
        margin-right: 20px;
    }

    .capnhapsachmoi .form-columns {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .capnhapsachmoi .form-group.full-width {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    .capnhapsachmoi .form-container {
        padding: 20px;
    }

    .capnhapsachmoi .button-group {
        flex-direction: column;
        align-items: center;
    }

    .capnhapsachmoi .btn-cancel,
    .capnhapsachmoi .btn-save {
        width: 100%;
        max-width: 200px;
    }
}



/* danhmuc  */
.capnhapdanhmuc{
    margin-left: 280px;
    display: flex;
    padding: 20px 30px;
    background-color: #72d0ff;
    width: 18%;
    border-radius: 10px;
}
.capnhapdanhmuc a{
    list-style-type: none;
    text-decoration: none;
    color: #0037ff;
}
.capnhapdanhmuc i{
margin-right: 10px;
    color: #0037ff;
}
.capnhapdanhmuc .danhmuc{
    display: none;
    list-style-type: none;
    position: absolute;
    width: 400px;
    height: 300px;
    background-color: #4fffb6;
    text-align: center;
    left: 450px;
    top: 188px;
    border-radius: 10px;

}
.capnhapdanhmuc:hover .danhmuc{
    display: block;
}
.capnhapdanhmuc .danhmuc li{
    padding: 25px 50px;
}

.capnhapdanhmuc .danhmuc li input{
    width: 75%;
    height: 35px;
}

.capnhapdanhmuc .danhmuc button{
    padding: 20px 70px;
    color: #ffffff;
    border-radius: 8px;
    margin-top: 50px;
}
.capnhapdanhmuc .danhmuc .luu{
    background-color: #72d0ff;
}
.capnhapdanhmuc .danhmuc .xoa{
    background-color: #f76f30;
}

.danhsachthongtin .thongtin1 {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.danhsachthongtin .thongtin1 tr th{
    padding: 15px 20px;
    background-color: #72d0ff;
    color: #ffffff;
    text-align: center;
    font-weight: bold;
    border: 1px solid #ddd;
}

.danhsachthongtin .thongtin1 tr td{
    padding: 12px 15px;
    color: #000000;
    text-align: center;
    border: 1px solid #ddd;
    background-color: #fff;
}

.danhsachthongtin .thongtin1 tr:nth-child(even) td {
    background-color: #f9f9f9;
}

.danhsachthongtin .thongtin1 tr:hover td {
    background-color: #f0f8ff;
}

.danhsachthongtin .thongtin1 tr td button{
    padding: 8px 15px;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin: 0 2px;
    font-size: 12px;
}

.danhsachthongtin .thongtin1 tr td .btn2{
    background-color: #0036fb;
}

.danhsachthongtin .thongtin1 tr td .btn2:hover{
    background-color: #002dd4;
}

.danhsachthongtin .thongtin1 tr td .btn3{
    background-color: #fa0000;
}

.danhsachthongtin .thongtin1 tr td .btn3:hover{
    background-color: #d40000;
}



/* Quản lí đơn hàng */


.donhang{
    margin-left: 280px;
    width: 42%;
    background-color: #d6d8d7;
    border-radius: 10px;
}

.donhang .danhsachdonhang{
    text-align: center;

}

.donhang .danhsachdonhang h3{
    background-color: #505050;
    padding: 25px;

}
.donhang .thongtindonhang{

}


.donhang .thongtindonhang tr{

}


.donhang .thongtindonhang {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.donhang .thongtindonhang th{
    padding: 15px 10px;
    background-color: #3c3a45;
    color: #ffffff;
    text-align: center;
    font-weight: bold;
    border: 1px solid #ddd;
}

.donhang .thongtindonhang td{
    padding: 12px 8px;
    text-align: center;
    border: 1px solid #ddd;
    background-color: #fff;
}

.donhang .thongtindonhang tr:nth-child(even) td {
    background-color: #f9f9f9;
}

.donhang .thongtindonhang tr:hover td {
    background-color: #f0f8ff;
}

.donhang .thongtindonhang button{
    padding: 8px 15px;
    color: #ffffff;
    background-color: #009456;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.donhang .thongtindonhang button:hover{
    background-color: #007a47;
}



.donhang .thongtindonhang td .btn3{}

/* Order status styles */
.status-pending {
    display: inline-block;
    padding: 5px 10px;
    background-color: #ffc107;
    color: #000;
    border-radius: 4px;
    font-weight: bold;
}

.status-transporting {
    display: inline-block;
    padding: 5px 10px;
    background-color: #17a2b8;
    color: #fff;
    border-radius: 4px;
    font-weight: bold;
}

.status-delivered {
    display: inline-block;
    padding: 5px 10px;
    background-color: #28a745;
    color: #fff;
    border-radius: 4px;
    font-weight: bold;
}

.status-cancelled {
    display: inline-block;
    padding: 5px 10px;
    background-color: #dc3545;
    color: #fff;
    border-radius: 4px;
    font-weight: bold;
}


/* doang thu  */

.doanhthu{
    margin-left: 280px;
    width: auto;
    margin-right: 10px;
    background-color: #e1e0e0;
}
.doanhthu .doanhthutheothang{
    text-align: center;

}
.doanhthu .doanhthutheothang h3{
    padding: 20px;
    background-color: #7a7979;
}
.doanhthu .theothang tr{

}
.doanhthu .theothang {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.doanhthu .theothang tr th{
    padding: 15px 20px;
    background-color: #0036fb;
    color: #ffffff;
    text-align: center;
    font-weight: bold;
    border: 1px solid #ddd;
}

.doanhthu .theothang tr td{
    padding: 12px 20px;
    text-align: center;
    border: 1px solid #ddd;
    background-color: #fff;
}

.doanhthu .theothang tr:nth-child(even) td {
    background-color: #f9f9f9;
}

.doanhthu .theothang tr:hover td {
    background-color: #f0f8ff;
}

/* CSS cho trang chỉnh sửa sách */
.capnhatsach {
    margin-left: 280px;
    margin-top: 20px;
    margin-right: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    min-height: auto;
}

.capnhatsach .form-container {
    padding: 30px;
}

.capnhatsach .form-container h2 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 24px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.capnhatsach .form-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.capnhatsach .form-column {
    display: flex;
    flex-direction: column;
}

.capnhatsach .form-group {
    margin-bottom: 20px;
}

.capnhatsach .form-group.full-width {
    grid-column: span 2;
}

.capnhatsach .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
    font-size: 14px;
}

.capnhatsach .form-group label .optional {
    font-weight: normal;
    color: #666;
    font-size: 12px;
    font-style: italic;
}

.capnhatsach .form-group input,
.capnhatsach .form-group select,
.capnhatsach .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.capnhatsach .form-group input:focus,
.capnhatsach .form-group select:focus,
.capnhatsach .form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.capnhatsach .form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.capnhatsach .current-image {
    margin: 10px 0;
}

.capnhatsach .current-image img {
    width: 120px;
    height: auto;
    border-radius: 6px;
    border: 2px solid #ddd;
    padding: 5px;
    background-color: #f8f9fa;
}

.capnhatsach .button-group {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.capnhatsach .btn-cancel,
.capnhatsach .btn-save {
    padding: 12px 30px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.capnhatsach .btn-cancel {
    background-color: #6c757d;
    color: white;
}

.capnhatsach .btn-cancel:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
}

.capnhatsach .btn-save {
    background-color: #007bff;
    color: white;
}

.capnhatsach .btn-save:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
}

/* Responsive design cho edit book */
@media (max-width: 1200px) {
    .capnhatsach {
        margin-left: 20px;
        margin-right: 20px;
    }

    .capnhatsach .form-columns {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .capnhatsach .form-group.full-width {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    .capnhatsach .form-container {
        padding: 20px;
    }

    .capnhatsach .button-group {
        flex-direction: column;
        align-items: center;
    }

    .capnhatsach .btn-cancel,
    .capnhatsach .btn-save {
        width: 100%;
        max-width: 200px;
    }
}

/* CSS cho trang chỉnh sửa danh mục */
.capnhatdanhmuc {
    margin-left: 280px;
    margin-top: 20px;
    padding: 30px;
    background-color: #e6e6e6;
    border-radius: 10px;
    width: 600px;
}

.capnhatdanhmuc .form-group h2 {
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.capnhatdanhmuc .input-group {
    margin-bottom: 20px;
}

.capnhatdanhmuc .input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.capnhatdanhmuc .input-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.capnhatdanhmuc .button-group {
    text-align: center;
    margin-top: 30px;
}

.capnhatdanhmuc .btn-cancel,
.capnhatdanhmuc .btn-save {
    padding: 10px 30px;
    margin: 0 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.capnhatdanhmuc .btn-cancel {
    background-color: #6c757d;
    color: white;
}

.capnhatdanhmuc .btn-save {
    background-color: #007bff;
    color: white;
}

.capnhatdanhmuc .btn-cancel:hover {
    background-color: #5a6268;
}

.capnhatdanhmuc .btn-save:hover {
    background-color: #0056b3;
}

/* CSS cho trang chi tiết đơn hàng */
.chitietdonhang {
    margin-left: 280px;
    margin-top: 20px;
    margin-right: 20px;
}

.chitietdonhang .thongtindonhang,
.chitietdonhang .danhsachsanpham,
.chitietdonhang .capnhatdonhang {
    background-color: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.chitietdonhang h3 {
    background-color: #6c757d;
    color: white;
    padding: 15px;
    margin: -20px -20px 20px -20px;
    border-radius: 10px 10px 0 0;
    text-align: center;
}

.chitietdonhang .order-info .info-row {
    display: flex;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.chitietdonhang .order-info .label {
    font-weight: bold;
    width: 200px;
    color: #333;
}

.chitietdonhang .order-info .value {
    flex: 1;
    color: #666;
}

.chitietdonhang .order-info .total-price {
    font-size: 18px;
    font-weight: bold;
    color: #28a745;
}

.chitietdonhang .chitiet-sanpham {
    width: 100%;
    border-collapse: collapse;
}

.chitietdonhang .chitiet-sanpham th {
    background-color: #007bff;
    color: white;
    padding: 12px;
    text-align: center;
    border: 1px solid #ddd;
}

.chitietdonhang .chitiet-sanpham td {
    padding: 10px;
    text-align: center;
    border: 1px solid #ddd;
}

.chitietdonhang .chitiet-sanpham tr:nth-child(even) {
    background-color: #f8f9fa;
}

.chitietdonhang .form-group {
    margin-bottom: 20px;
}

.chitietdonhang .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.chitietdonhang .form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.chitietdonhang .button-group {
    text-align: center;
}

.chitietdonhang .btn-back,
.chitietdonhang .btn-save,
.chitietdonhang .btn-cancel {
    padding: 10px 20px;
    margin: 0 5px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.chitietdonhang .btn-back {
    background-color: #6c757d;
    color: white;
}

.chitietdonhang .btn-save {
    background-color: #28a745;
    color: white;
}

.chitietdonhang .btn-cancel {
    background-color: #dc3545;
    color: white;
}

.chitietdonhang .btn-back:hover {
    background-color: #5a6268;
}

.chitietdonhang .btn-save:hover {
    background-color: #218838;
}

.chitietdonhang .btn-cancel:hover {
    background-color: #c82333;
}

/* CSS cho trang chỉnh sửa người dùng */
.capnhatuser {
    margin-left: 280px;
    margin-top: 20px;
    padding: 30px;
    background-color: #e6e6e6;
    border-radius: 10px;
    width: 700px;
}

.capnhatuser .error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
}

.capnhatuser .form-group h2 {
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.capnhatuser .input-group {
    margin-bottom: 20px;
}

.capnhatuser .input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.capnhatuser .input-group input,
.capnhatuser .input-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.capnhatuser .button-group {
    text-align: center;
    margin-top: 30px;
}

.capnhatuser .btn-cancel,
.capnhatuser .btn-save {
    padding: 10px 30px;
    margin: 0 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.capnhatuser .btn-cancel {
    background-color: #6c757d;
    color: white;
}

.capnhatuser .btn-save {
    background-color: #007bff;
    color: white;
}

.capnhatuser .btn-cancel:hover {
    background-color: #5a6268;
}

.capnhatuser .btn-save:hover {
    background-color: #0056b3;
}
