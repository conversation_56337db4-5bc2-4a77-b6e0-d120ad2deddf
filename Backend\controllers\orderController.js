const Order = require('../models/orderModel');
const Cart = require('../models/cartModel');
const Book = require('../models/bookModel');
const Category = require('../models/categoryModel');

async function generateOrderCode() {
  // Optimize by selecting only the orderCode field
  const lastOrder = await Order.findOne()
    .sort({ createdAt: -1 })
    .select('orderCode')
    .lean();

  let nextNumber = 1;

  if (lastOrder && lastOrder.orderCode) {
    const match = lastOrder.orderCode.match(/OD(\d+)/);
    if (match && match[1]) {
      nextNumber = parseInt(match[1]) + 1;
    }
  }

  return `#OD${String(nextNumber).padStart(2, '0')}`;
}

const createOrder = async function(req, res) {
  try {


    const { items, totalPrice, note, paymentMethod } = req.body;
    const user = req.user;

    if (!paymentMethod) {
      return res.status(400).send('<PERSON><PERSON><PERSON> chọn phương thức thanh toán');
    }

    if (!items || Object.keys(items).length === 0) {
      console.error('Không có sản phẩm trong đơn hàng');
      return res.status(400).send('Không có sản phẩm trong đơn hàng');
    }

    const userAddress = user.address || {};
    const fullname = userAddress.fullname || 'Ẩn danh';
    const phone = userAddress.phone || '';
    const address = userAddress.address ?
      `${userAddress.address}, ${userAddress.ward}, ${userAddress.district}, ${userAddress.city}` :
      '';

    console.log('Thông tin địa chỉ:', { fullname, phone, address });

    // Chuyển đổi items từ object thành array
    const formattedItems = Object.values(items).map(item => ({
      book: item.book,
      quantity: parseInt(item.quantity),
      price: parseFloat(item.price)
    }));



    // Kiểm tra stock trước khi tạo đơn hàng
    for (const item of formattedItems) {
      const book = await Book.findById(item.book).select('title stock');
      if (!book) {
        return res.status(400).send(`Sách với ID ${item.book} không tồn tại`);
      }
      if ((book.stock || 0) < item.quantity) {
        return res.status(400).send(`Sách "${book.title}" không đủ số lượng trong kho. Còn lại: ${book.stock || 0}`);
      }
    }

    const order = new Order({
      orderCode: await generateOrderCode(),
      user: user._id,
      items: formattedItems,
      totalPrice: parseFloat(totalPrice),
      address: { fullname, phone, address },
      note: note || '',
      paymentMethod: paymentMethod
    });

    const savedOrder = await order.save();
    console.log('Đã lưu đơn hàng:', savedOrder._id);

    // Cập nhật stock và sold cho từng sách bằng loop
    for (const item of formattedItems) {
      try {
        // Sử dụng findByIdAndUpdate thay vì save() để tránh validation issues
        const book = await Book.findById(item.book).select('title stock sold');
        if (book) {
          const newStock = Math.max(0, (book.stock || 0) - item.quantity);
          const newSold = (book.sold || 0) + item.quantity;

          // Cập nhật trực tiếp trong database
          await Book.findByIdAndUpdate(
            item.book,
            {
              $set: {
                stock: newStock,
                sold: newSold
              }
            },
            { new: true }
          );
        }
      } catch (error) {
        console.error(`Lỗi khi cập nhật sách ${item.book}:`, error);
      }
    }

    // Kiểm tra đơn hàng đã được lưu chưa
    const checkOrder = await Order.findById(savedOrder._id)
      .populate('items.book', 'title image price')
      .lean();

    if (checkOrder) {
      console.log('Đơn hàng đã được lưu thành công:', {
        id: checkOrder._id,
        orderCode: checkOrder.orderCode,
        status: checkOrder.status,
        items: checkOrder.items.length,
        totalPrice: checkOrder.totalPrice
      });
    } else {
      console.error('Không tìm thấy đơn hàng sau khi lưu!');
    }

    // Xóa giỏ hàng sau khi đặt hàng thành công
    await Cart.deleteOne({ userId: user._id });
    console.log('Đã xóa giỏ hàng');

    res.redirect('/customer/orders');
  } catch (error) {
    console.error('Lỗi khi tạo đơn hàng:', error);
    res.status(500).send('Lỗi khi tạo đơn hàng: ' + error.message);
  }
};

const getMyOrders = async function(req, res) {
  try {
    // Add pagination and optimize query
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Add status filter if provided
    const statusFilter = req.query.status && req.query.status !== 'all'
      ? { user: req.user._id, status: req.query.status }
      : { user: req.user._id };

    console.log('Tìm đơn hàng với filter:', statusFilter);

    // Run queries in parallel
    const [orders, total] = await Promise.all([
      Order.find(statusFilter)
        .select('orderCode items totalPrice status createdAt')
        .populate('items.book', 'title image price sold')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Order.countDocuments(statusFilter)
    ]);

    console.log('Tìm thấy đơn hàng:', orders.length > 0 ?
      `${orders.length} đơn hàng` : 'Không có đơn hàng nào');

    // Kiểm tra đánh giá cho từng đơn hàng đã giao
    const Review = require('../models/reviewModel');
    for (let order of orders) {
      if (order.status === 'delivered') {
        // Kiểm tra xem đã có đánh giá cho sách trong đơn hàng này chưa
        for (let item of order.items) {
          if (item.book) {
            const existingReview = await Review.findOne({
              user: req.user._id,
              book: item.book._id
            });
            item.hasReview = !!existingReview;
            if (existingReview) {
              item.reviewId = existingReview._id;
            }
          }
        }
      }
    }

    if (orders.length > 0) {
      console.log('Mẫu đơn hàng đầu tiên:', {
        id: orders[0]._id,
        orderCode: orders[0].orderCode,
        status: orders[0].status,
        items: orders[0].items.length,
        totalPrice: orders[0].totalPrice
      });
    }

    // Lấy categories cho header
    const categories = await Category.find().sort({ name: 1 });

    res.render('customer/order/order', {
      orders,
      currentStatus: req.query.status || 'all',
      user: req.user,
      categories: categories,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Lỗi khi tải đơn hàng:', error);
    const categories = await Category.find().sort({ name: 1 });
    res.status(500).render('customer/order/order', {
      orders: [],
      error: 'Không thể tải đơn hàng của bạn',
      user: req.user,
      categories: categories,
      pagination: {
        total: 0,
        page: 1,
        pages: 0
      }
    });
  }
};



module.exports = { createOrder, getMyOrders };
