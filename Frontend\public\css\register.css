body {
    background: #fff7f0;
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}
  
.container {
    max-width: 400px;
    margin: 80px auto;
    background: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
}
  
h2 {
    text-align: center;
    color: #444;
    margin-bottom: 25px;
}
  
input[type="text"],
  input[type="email"],
  input[type="password"] {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    font-size: 15px;
    border: 1px solid #ccc;
    border-radius: 5px;
}
  
button {
    width: 100%;
    padding: 10px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
}
  
button:hover {
    background-color: #1e7e34;
  }
  
  p {
    text-align: center;
    margin-top: 15px;
  }
  
  p a {
    color: #28a745;
    text-decoration: none;
  }
  
  p.error {
    color: red;
    margin-bottom: 10px;
    text-align: center;
}
  