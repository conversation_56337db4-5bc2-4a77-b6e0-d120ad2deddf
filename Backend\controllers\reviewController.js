const Order = require('../models/orderModel');
const Review = require('../models/reviewModel');

const addReview = async function(req, res) {
  const { rating, comment } = req.body;
  const { bookId } = req.params;
  const userId = req.user._id;
  try {
    const order = await Order.findOne({
      user: userId,
      'items.book': bookId,
      status: 'delivered'
    });

    if (!order) {
      return res.status(403).json({ message: "Bạn phải mua sách này để đánh giá." });
    }

    const existingReview = await Review.findOne({ user: userId, book: bookId });
    if (existingReview) {
      return res.status(400).json({ message: "Bạn đã đánh giá sách này rồi." });
    }

    const review = new Review({
      user: userId,
      book: bookId,
      rating,
      comment
    });

    await review.save();
    res.status(201).json({ message: "<PERSON><PERSON><PERSON> gi<PERSON> thành công", review });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Lỗi server" });
  }
};

const getReviewsByBook = async function(req, res) {
  try {
    const { bookId } = req.params;
    const reviews = await Review.find({ book: bookId }).populate("user", "username");

    res.json(reviews);
  } catch (err) {
    res.status(500).json({ message: "Lỗi server" });
  }
};

const addReviewFromOrder = async function(req, res)  {
  try {
    const { orderItemId, rating, content } = req.body;
    const userId = req.user._id;

    console.log('Review data received:', { orderItemId, rating, content, userId });

    // Tìm order chứa orderItemId
    const order = await Order.findOne({
      user: userId,
      'items._id': orderItemId,
      status: 'delivered'
    });

    if (!order) {
      return res.redirect('/customer/orders?error=Không tìm thấy đơn hàng hoặc đơn hàng chưa được giao');
    }

    // Tìm item cụ thể trong order
    const orderItem = order.items.find(item => item._id.toString() === orderItemId);
    if (!orderItem) {
      return res.redirect('/customer/orders?error=Không tìm thấy sản phẩm trong đơn hàng');
    }

    const bookId = orderItem.book;

    console.log('Order item found:', { orderItemId, bookId, orderItem });

    // Kiểm tra bookId có hợp lệ không
    if (!bookId || bookId === 'null' || bookId === null) {
      return res.redirect('/customer/orders?error=Sản phẩm không hợp lệ');
    }

    // Cho phép đánh giá nhiều lần (bỏ kiểm tra existingReview)

    // Kiểm tra xem sản phẩm này trong đơn hàng đã được đánh giá chưa
    const existingReview = await Review.findOne({
      user: userId,
      order: order._id,
      book: bookId
    });

    if (existingReview) {
      return res.redirect('/customer/orders?error=Bạn đã đánh giá sản phẩm này rồi');
    }

    // Tạo đánh giá mới
    const review = new Review({
      user: userId,
      book: bookId,
      order: order._id,
      rating: parseInt(rating),
      comment: content
    });

    await review.save();

    res.redirect('/customer/orders?success=Đánh giá của bạn đã được gửi thành công');
  } catch (error) {
    console.error('Lỗi khi thêm đánh giá:', error);
    res.redirect('/customer/orders?error=Đã xảy ra lỗi khi gửi đánh giá');
  }
};

const getUserReviewForBook = async function(req, res) {
  try {
    const { bookId } = req.params;
    const userId = req.user._id;

    const review = await Review.findOne({ user: userId, book: bookId })
      .populate('user', 'username')
      .populate('book', 'title')
      .lean();

    if (!review) {
      return res.status(404).json({ message: "Không tìm thấy đánh giá" });
    }

    res.json(review);
  } catch (error) {
    console.error('Lỗi khi lấy đánh giá:', error);
    res.status(500).json({ message: "Lỗi server" });
  }
};

// Xem đánh giá
const getReviewById = async function(req, res) {
  try {
    const reviewId = req.params.reviewId;
    const review = await Review.findById(reviewId)
      .populate('user', 'username')
      .populate('book', 'title author image price')
      .populate('order', 'createdAt totalPrice')
      .lean();

    if (!review) {
      return res.redirect('/customer/orders?error=Không tìm thấy đánh giá');
    }

    // Kiểm tra quyền xem đánh giá (chỉ người tạo mới được xem)
    if (review.user._id.toString() !== req.user._id.toString()) {
      return res.redirect('/customer/orders?error=Bạn không có quyền xem đánh giá này');
    }

    res.render('customer/review/view-review', {
      review: review,
      user: req.user
    });
  } catch (error) {
    console.error('Lỗi khi xem đánh giá:', error);
    res.redirect('/customer/orders?error=Đã xảy ra lỗi khi xem đánh giá');
  }
};

module.exports = {
  addReview,
  getReviewsByBook,
  addReviewFromOrder,
  getUserReviewForBook,
  getReviewById
};
