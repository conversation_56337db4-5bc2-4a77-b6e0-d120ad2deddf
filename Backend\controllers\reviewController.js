const Order = require('../models/orderModel');
const Review = require('../models/reviewModel');

const addReview = async function(req, res) {
  const { rating, comment } = req.body;
  const { bookId } = req.params;
  const userId = req.user._id;
  try {
    const order = await Order.findOne({
      user: userId,
      'items.book': bookId,
      status: 'delivered'
    });

    if (!order) {
      return res.status(403).json({ message: "Bạn phải mua sách này để đánh giá." });
    }

    const existingReview = await Review.findOne({ user: userId, book: bookId });
    if (existingReview) {
      return res.status(400).json({ message: "Bạn đã đánh giá sách này rồi." });
    }

    const review = new Review({
      user: userId,
      book: bookId,
      rating,
      comment
    });

    await review.save();
    res.status(201).json({ message: "<PERSON><PERSON><PERSON> gi<PERSON> thành công", review });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Lỗi server" });
  }
};

const getReviewsByBook = async function(req, res) {
  try {
    const { bookId } = req.params;
    const reviews = await Review.find({ book: bookId }).populate("user", "username");

    res.json(reviews);
  } catch (err) {
    res.status(500).json({ message: "Lỗi server" });
  }
};

const addReviewFromOrder = async function(req, res)  {
  try {
    const { orderItemId, rating, content } = req.body;
    const userId = req.user._id;

    // Kiểm tra xem đơn hàng có tồn tại và thuộc về người dùng không
    const order = await Order.findOne({
      _id: orderItemId,
      user: userId,
      status: 'delivered'
    });

    if (!order) {
      return res.redirect('/customer/orders?error=Không tìm thấy đơn hàng hoặc đơn hàng chưa được giao');
    }

    // Lấy thông tin sách từ đơn hàng
    const bookId = order.items[0].book;

    // Cho phép đánh giá nhiều lần (bỏ kiểm tra existingReview)

    // Tạo đánh giá mới
    const review = new Review({
      user: userId,
      book: bookId,
      rating: parseInt(rating),
      comment: content
    });

    await review.save();

    res.redirect('/customer/orders?success=Đánh giá của bạn đã được gửi thành công');
  } catch (error) {
    console.error('Lỗi khi thêm đánh giá:', error);
    res.redirect('/customer/orders?error=Đã xảy ra lỗi khi gửi đánh giá');
  }
};

const getUserReviewForBook = async function(req, res) {
  try {
    const { bookId } = req.params;
    const userId = req.user._id;

    const review = await Review.findOne({ user: userId, book: bookId })
      .populate('user', 'username')
      .populate('book', 'title')
      .lean();

    if (!review) {
      return res.status(404).json({ message: "Không tìm thấy đánh giá" });
    }

    res.json(review);
  } catch (error) {
    console.error('Lỗi khi lấy đánh giá:', error);
    res.status(500).json({ message: "Lỗi server" });
  }
};

module.exports = {
  addReview,
  getReviewsByBook,
  addReviewFromOrder,
  getUserReviewForBook
};
