const Book = require("../models/bookModel");
const Review = require("../models/reviewModel");
const Category = require("../models/categoryModel");
const jwt = require("jsonwebtoken");
const User = require("../models/userModel");

const getHomepageBooks = async function(req, res) {
  try {
    let user = null;
    const token = req.cookies.token;
    if (token) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        user = await User.findById(decoded.id).select('username role');
      } catch (err) {
        console.log("Token không hợp lệ");
      }
    }
    // L<PERSON>y danh sách sách theo từng danh mục và danh mục
    const [banChay, manga, comboSach, sachMoi, tamlyHoc, selfHelp, categories] = await Promise.all([
      Book.find({ group: 'banchay' }).limit(8),
      Book.find({ group: 'manga' }).limit(8),
      Book.find({ group: 'combo' }).limit(8),
      Book.find({ group: 'sachmoi' }).limit(8),
      Book.find({ group: 'tamlyhoc' }).limit(8),
      Book.find({ group: 'selfhelp' }).limit(8),
      Category.find().sort({ name: 1 })
    ]);
    // Render trang chủ với dữ liệu
    res.render('home', {
      user: user,
      banChay: banChay,
      manga: manga,
      comboSach: comboSach,
      sachMoi: sachMoi,
      tamlyHoc: tamlyHoc,
      selfHelp: selfHelp,
      categories: categories,
      category: req.query.category || null,
      page: req.query.page || 1
    });
  } catch (error) {
    console.error("Lỗi khi lấy dữ liệu sách:", error);
    res.status(500).send('Lỗi khi tải trang chủ');
  }
}

// lấy sách theo danh mục
const getBooksByCategory = async function(req, res) {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;
    const categoryParam = req.params.categoryName;
    let query = {};
    let categoryName = categoryParam;
    if (/^[0-9a-fA-F]{24}$/.test(categoryParam)) {
      const category = await Category.findById(categoryParam);
      if (category) {
        query = { category: categoryParam };
        categoryName = category.name;
      } else {
        return res.render('product/category', {
          books: [],
          category: 'Danh mục không tồn tại',
          categories: [],
          pagination: { total: 0, page: 1, pages: 0 }
        });
      }
    } else if (categoryParam && categoryParam !== 'all') {
      query = {
        $or: [
          { category: categoryParam },
          { group: categoryParam }
        ]
      };
    }
    const minPrice = parseInt(req.query.minPrice);
    const maxPrice = parseInt(req.query.maxPrice);
    if (minPrice || maxPrice) {
      query.price = {};
      if (minPrice) {
        query.price.$gte = minPrice;
      }
      if (maxPrice) {
        query.price.$lte = maxPrice;
      }
    }

    const [books, total] = await Promise.all([
      Book.find(query)
        .select('title author price image _id sold category group')
        .populate('category', 'name')
        .skip(skip)
        .limit(limit)
        .lean(),
      Book.countDocuments(query)
    ]);
    const allCategories = await Category.find().sort({ name: 1 });
    res.render('product/category', {
      books,
      category: categoryName,
      categories: allCategories,
      req: req,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error in getBooksByCategory:', error);
    res.status(500).json({ message: "Lỗi server" });
  }
};

const getBooks = async function(req, res) {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    let query = {};
    const minPrice = parseInt(req.query.minPrice);
    const maxPrice = parseInt(req.query.maxPrice);
    if (minPrice || maxPrice) {
      query.price = {};
      if (minPrice) {
        query.price.$gte = minPrice;
      }
      if (maxPrice) {
        query.price.$lte = maxPrice;
      }
    }
    const [books, total, categories] = await Promise.all([
      Book.find(query)
        .select('title author price image _id category sold')
        .skip(skip)
        .limit(limit)
        .lean(),
      Book.countDocuments(query),
      Category.find().sort({ name: 1 })
    ]);
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      return res.json({
        books,
        pagination: {
          total,
          page,
          pages: Math.ceil(total / limit)
        }
      });
    }

    // Vì không có view books/all, redirect về trang chủ
    res.redirect('/');
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Lỗi server" });
  }
};

// Hàm lấy thông tin chi tiết của một cuốn sách theo ID
const getBookById = async function(req, res) {
  try {
    const bookId = req.params.id;
    const book = await Book.findById(bookId).lean();
    const reviews = await Review.find({ book: bookId })
      .populate("user", "username")
      .lean();
    if (!book) {
      return res.status(404).send("Không tìm thấy sách");
    }

    let averageRating = null;
    if (reviews.length > 0) {
      let totalRating = 0;
      for (let i = 0; i < reviews.length; i++) {
        totalRating += reviews[i].rating;
      }

      const avgRating = totalRating / reviews.length;
      averageRating = parseFloat(avgRating.toFixed(1));
    }
    let user = null;
    if (req.user) {
      user = req.user;
    } else {
      const token = req.cookies.token;
      if (token) {
        try {
          const decoded = jwt.verify(token, process.env.JWT_SECRET);
          user = await User.findById(decoded.id)
            .select("username role")
            .lean();
        } catch (err) {
          console.log("Token không hợp lệ:", err.message);
        }
      }
    }

    // Tìm đánh giá của user hiện tại (nếu có)
    let userReview = null;
    if (user) {
      userReview = reviews.find(review =>
        review.user && review.user._id.toString() === user._id.toString()
      );
    }

    const currentPage = parseInt(req.query.page) || 1;
    res.render("product/product", {
      book: book,
      reviews: reviews,
      averageRating: averageRating,
      totalReviews: reviews.length,
      user: user,
      userReview: userReview,
      page: currentPage
    });
  } catch (error) {
    console.error(error);
    res.status(500).send("Lỗi server");
  }
}

// Hàm tìm kiếm sách
const searchBooks = async function(req, res) {
  try {
    const query = req.query.q || '';
    const page = parseInt(req.query.page) || 1;
    const limit = 12;
    const skip = (page - 1) * limit;

    if (!query.trim()) {
      return res.redirect('/');
    }

    // Tạo regex để tìm kiếm không phân biệt hoa thường
    const searchRegex = new RegExp(query.trim(), 'i');

    // Tìm kiếm trong title, author, publisher, description
    const searchConditions = {
      $or: [
        { title: searchRegex },
        { author: searchRegex },
        { publisher: searchRegex },
        { description: searchRegex }
      ]
    };

    // Thực hiện tìm kiếm với pagination
    const [books, total, categories] = await Promise.all([
      Book.find(searchConditions)
        .select('title author price image stock sold')
        .skip(skip)
        .limit(limit)
        .lean(),
      Book.countDocuments(searchConditions),
      Category.find().sort({ name: 1 })
    ]);

    // Tính toán pagination
    const totalPages = Math.ceil(total / limit);

    // Lấy thông tin user
    let user = null;
    if (req.user) {
      user = req.user;
    } else {
      const token = req.cookies.token;
      if (token) {
        try {
          const decoded = jwt.verify(token, process.env.JWT_SECRET);
          user = await User.findById(decoded.id)
            .select("username role")
            .lean();
        } catch (err) {
          console.log("Token không hợp lệ:", err.message);
        }
      }
    }

    res.render('search/search-results', {
      books,
      query,
      total,
      currentPage: page,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
      nextPage: page + 1,
      prevPage: page - 1,
      user,
      categories
    });

  } catch (error) {
    console.error('Lỗi tìm kiếm:', error);
    res.status(500).send('Lỗi server khi tìm kiếm');
  }
};

// API tìm kiếm gợi ý (autocomplete)
const searchSuggestions = async function(req, res) {
  try {
    console.log('Search suggestions API called with query:', req.query.q);
    const query = req.query.q || '';

    if (!query.trim() || query.length < 1) {
      console.log('Query too short, returning empty array');
      return res.json([]);
    }

    const searchRegex = new RegExp(query.trim(), 'i');

    console.log('Searching with regex:', searchRegex);

    const suggestions = await Book.find({
      $or: [
        { title: searchRegex },
        { author: searchRegex },
        { publisher: searchRegex }
      ]
    })
    .select('_id title author publisher image price')
    .limit(8)
    .lean();

    console.log('Found suggestions:', suggestions.length);

    // Sắp xếp theo độ ưu tiên: title match trước, sau đó author, cuối cùng publisher
    const prioritizedBooks = [];
    const seen = new Set();

    // Ưu tiên sách có title match trước
    suggestions.forEach(book => {
      if (book.title && book.title.toLowerCase().includes(query.toLowerCase()) && !seen.has(book._id.toString())) {
        prioritizedBooks.push({
          ...book,
          matchType: 'title'
        });
        seen.add(book._id.toString());
      }
    });

    // Thêm sách có author match
    suggestions.forEach(book => {
      if (book.author && book.author.toLowerCase().includes(query.toLowerCase()) && !seen.has(book._id.toString())) {
        prioritizedBooks.push({
          ...book,
          matchType: 'author'
        });
        seen.add(book._id.toString());
      }
    });

    // Thêm sách có publisher match
    suggestions.forEach(book => {
      if (book.publisher && book.publisher.toLowerCase().includes(query.toLowerCase()) && !seen.has(book._id.toString())) {
        prioritizedBooks.push({
          ...book,
          matchType: 'publisher'
        });
        seen.add(book._id.toString());
      }
    });

    const finalResult = prioritizedBooks.slice(0, 6);
    console.log('Returning final result:', finalResult);
    res.json(finalResult);

  } catch (error) {
    console.error('Lỗi lấy gợi ý:', error);
    res.json([]);
  }
};

module.exports = {
  getHomepageBooks,
  getBooksByCategory,
  getBooks,
  getBookById,
  searchBooks,
  searchSuggestions
};
