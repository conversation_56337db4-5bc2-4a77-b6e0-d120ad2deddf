// Header mới theo thiết kế
header.new-header
    div(class="container header-container")
        div(class="logo")
            img(src="./public/img/fahasa-logo.webp" alt="logo")
        ul(class="menu")
            li
                i(class="fa-solid fa-bars")
                ul(class="menu-genre")
                    li: a(href="/books/category/Văn học") <PERSON><PERSON><PERSON> học
                    li: a(href="/books/category/Kinh tế") Kinh tế
                    li: a(href="/books/category/Sách thiếu nhi") Sách thiếu nhi
                    li: a(href="/books/category/Tâm lí-Kĩ năng sống") Tâm lí-Kĩ năng sống
                    li: a(href="/books/category/Nuôi dạy con") Nuôi dạy con
                    li: a(href="/books/category/Giáo khoa-Tham khảo") Giáo khoa-Tham khảo
                    li: a(href="/books/category/Giáo ngôn ngữ") G<PERSON><PERSON>o ngôn ngữ
        div(class="header-search")
            form(action="/search" method="GET")
                input(type="text", placeholder="Tìm kiếm sản phẩm", name="q")
                input(type="submit" name="search" class="submit-product" value="Tìm")
        div(class="card__head")
            a(href="/customer")
                i(class="fa-solid fa-user")
                p Tài khoản
            a(href="/cart" class="card")
                i(class="fa-solid fa-cart-shopping")
                p Giỏ hàng

            if user && user.role === 'admin'
                a(href="/admin" class="admin-link")
                    i(class="fa-solid fa-cog")
                    p Admin

// Header cũ (giữ nguyên)
header
    div(class="container header-container")
        div(class="logo")
            a(href="/")
                h2 BOOKSTORE
        ul(class="menu")
            li
                i(class="fa-solid fa-bars")
                ul(class="menu-genre")
                    if categories && categories.length > 0
                        each category in categories
                            li: a(href=`/books/category/${category.name}`)= category.name
                    else
                        li: span(style="color: #999;") Chưa có danh mục
        div(class="header-search")
            form(action="/search" method="GET" id="search-form")
                div.search-input-container
                    input(type="text", placeholder="Tìm kiếm sản phẩm", name="q", id="search-input", autocomplete="off")
                    button(type="submit" name="search" class="submit-product")
                        i.fa-solid.fa-magnifying-glass
                    div.search-suggestions(id="search-suggestions")
        div(class="card__head")
            a(href="/customer")
                i(class="fa-solid fa-user")
                p Tài khoản
            a(href="/cart" class="card")
                i(class="fa-solid fa-cart-shopping")
                p Giỏ hàng

            if user && user.role === 'admin'
                a(href="/admin" class="admin-link")
                    i(class="fa-solid fa-cog")
                    p Admin

script(src='/public/js/search.js')