header
    div(class="container header-container")
        div(class="logo")
            a(href="/")
                h2 BOOKSTORE
        ul(class="menu")
            li
                i(class="fa-solid fa-bars")
                ul(class="menu-genre")
                    if categories && categories.length > 0
                        each category in categories
                            li: a(href=`/books/category/${category._id}`)= category.name.toUpperCase()
                    else
                        li: span(style="color: #999;") Chưa có danh mục
        div(class="header-search")
            form(action="/search" method="GET" id="search-form")
                div.search-input-container
                    input(type="text", placeholder="Tìm kiếm sản phẩm", name="q", id="search-input", autocomplete="off")
                    input(type="submit" name="search" class="submit-product" value="Tìm")
                    div.search-suggestions(id="search-suggestions")
        div(class="card__head")
            a(href="/customer")
                i(class="fa-solid fa-user")
                p <PERSON><PERSON><PERSON> k<PERSON>n
            a(href="/cart" class="card")
                i(class="fa-solid fa-cart-shopping")
                p Giỏ hàng
            if user && user.role === 'admin'
                a(href="/admin" class="admin-link")
                    i(class="fa-solid fa-cog")
                    p Admin

script(src='/public/js/search.js')
script.
  // Debug script để kiểm tra
  console.log('Header script loaded');
  document.addEventListener('DOMContentLoaded', function() {
    console.log('Header DOM loaded');
    const searchInput = document.getElementById('search-input');
    const searchSuggestions = document.getElementById('search-suggestions');
    console.log('Header - Search input:', searchInput);
    console.log('Header - Search suggestions:', searchSuggestions);

    if (searchInput) {
      searchInput.addEventListener('input', function(e) {
        console.log('Header - Input detected:', e.target.value);
      });
    }
  });