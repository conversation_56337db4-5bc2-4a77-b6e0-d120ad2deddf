
header
    div(class="container header-container")
        div(class="logo")
            a(href="/")
                h2 BOOKSTORE
        ul(class="menu")
            li
                i(class="fa-solid fa-bars")
                ul(class="menu-genre")
                    if categories && categories.length > 0
                        each category in categories
                            li: a(href=`/books/category/${category.name}`)= category.name
                    else
                        li: span(style="color: #999;") Chưa có danh mục
        div(class="header-search")
            form(action="/search" method="GET" id="search-form")
                div.search-input-container
                    input(type="text", placeholder="Tìm kiếm sản phẩm", name="q", id="search-input", autocomplete="off")
                    button(type="submit" name="search" class="submit-product")
                        i.fa-solid.fa-magnifying-glass
                    div.search-suggestions(id="search-suggestions")
        div(class="card__head")
            div(class="notify")
                a(href="#")
                    div(class="notify-bell")
                        i(class="fa-solid fa-bell")
                        p <PERSON>h<PERSON>ng b<PERSON>o
                ul(class="notify-content")
                    //- T<PERSON><PERSON> cả link đề<PERSON> chuy<PERSON> sang trang Thông báo ở customer
                    li: a(href="#")
                        img(src="./public/img/1.jpg", alt="")
                        p Đơn hàng
                            span q0jTfl0wq842nDjs
                            |  đã được Xác nhận, đơn hàng của bạn đang được giao giao hàng
                    li: a(href="#")
                        img(src="./public/img/1.jpg", alt="")
                        p Đơn hàng
                            span q0jTfl0wq842nDjs
                            |  đã được shipper lấy hàng, vui lòng để ý điện thoại
                    li: a(href="#")
                        img(src="./public/img/1.jpg", alt="")
                        p Đơn hàng
                            span q0jTfl0wq842nDjs
                            |  đã được giao tới bạn
                    li: a(href="#")
                        img(src="./public/img/1.jpg", alt="")
                        p Đơn hàng
                            span q0jTfl0wq842nDjs
                            |  đã được Xác nhận, đơn hàng của bạn đang được giao giao hàng
                    li: a(href="#")
                        img(src="./public/img/1.jpg", alt="")
                        p Đơn hàng
                            span q0jTfl0wq842nDjs
                            |  đã được Xác nhận, đơn hàng của bạn đang được giao giao hàng
                    li: a(href="") Xem thêm
            a(href="/customer")
                i(class="fa-solid fa-user")
                p Tài khoản
            a(href="/cart" class="card")
                i(class="fa-solid fa-cart-shopping")
                p Giỏ hàng

            if user && user.role === 'admin'
                a(href="/admin" class="admin-link")
                    i(class="fa-solid fa-cog")
                    p Admin

script(src='/public/js/search.js')