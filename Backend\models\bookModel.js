const mongoose = require("mongoose");

const bookSchema = new mongoose.Schema({
  code: {
    type: String,
    required: true,
    unique: true
  },
  title: { type: String, required: true },
  author: { type: String },
  publisher: { type: String },
  supplier: { type: String },
  year: { type: Number },
  bookLanguage: { type: String },
  format: { type: String },
  pageCount: { type: Number },
  price: { type: Number, required: true },
  stock: { type: Number, default: 0 },
  sold: { type: Number, default: 0 },
  category: { type: mongoose.Schema.Types.ObjectId, ref: 'Category' },
  group: { type: String },
  description: { type: String },
  image: { type: String },
}, { timestamps: true });

module.exports = mongoose.model("Book", bookSchema);