document.addEventListener("DOMContentLoaded", function () {
  const provinceSelect = document.getElementById("province");
  const districtSelect = document.getElementById("district");
  const wardSelect = document.getElementById("ward");

  // Lưu giá trị hiện tại để khôi phục sau khi tải dữ liệu mới
  const currentProvince = provinceSelect.value;
  const currentDistrict = districtSelect.value;
  const currentWard = wardSelect.value;

  console.log("Giá trị hiện tại:", {
    province: currentProvince,
    district: currentDistrict,
    ward: currentWard
  });

  // Thêm dataset.code cho các option tĩnh
  fetch("https://provinces.open-api.vn/api/?depth=1")
    .then((res) => res.json())
    .then((data) => {
      // Thêm dataset.code cho các option tĩnh
      for (let i = 0; i < provinceSelect.options.length; i++) {
        const option = provinceSelect.options[i];
        if (option.value) {
          const province = data.find(p => p.name === option.value);
          if (province) {
            option.dataset.code = province.code;
          }
        }
      }

      // Nếu đã có giá trị tỉnh/thành phố, tải quận/huyện tương ứng
      if (currentProvince) {
        const selectedOption = Array.from(provinceSelect.options).find(opt => opt.value === currentProvince);
        if (selectedOption && selectedOption.dataset.code) {
          loadDistricts(selectedOption.dataset.code, currentDistrict, currentWard);
        } else {
          const province = data.find(p => p.name === currentProvince);
          if (province) {
            loadDistricts(province.code, currentDistrict, currentWard);
          }
        }
      }
    });

  // Chỉ tải thêm tỉnh/thành phố nếu không có sẵn trong select
  if (provinceSelect.options.length <= 4) { // Đã có 4 option: mặc định + 3 thành phố
    fetch("https://provinces.open-api.vn/api/?depth=1")
      .then((res) => res.json())
      .then((data) => {
        // Lưu lại các option hiện có
        const existingOptions = Array.from(provinceSelect.options).map(opt => opt.value);

        data.forEach((province) => {
          // Chỉ thêm các tỉnh/thành phố chưa có trong select
          if (!existingOptions.includes(province.name)) {
            const opt = document.createElement("option");
            opt.value = province.name;  // Lưu tên thay vì mã code
            opt.text = province.name;
            opt.dataset.code = province.code;  // Lưu mã code vào data attribute để sử dụng khi cần
            provinceSelect.add(opt);
          }
        });

        // Đảm bảo giá trị hiện tại được chọn
        if (currentProvince) {
          for (let i = 0; i < provinceSelect.options.length; i++) {
            if (provinceSelect.options[i].value === currentProvince) {
              provinceSelect.selectedIndex = i;
              break;
            }
          }
        }
      });
  }

  // Hàm tải quận/huyện
  function loadDistricts(provinceCode, selectedDistrict, selectedWard) {
    fetch(`https://provinces.open-api.vn/api/p/${provinceCode}?depth=2`)
      .then((res) => res.json())
      .then((data) => {
        // Lưu lại option đầu tiên
        const defaultOption = districtSelect.options[0];
        districtSelect.innerHTML = '';
        districtSelect.add(defaultOption);

        data.districts.forEach((district) => {
          const opt = document.createElement("option");
          opt.value = district.name;  // Lưu tên thay vì mã code
          opt.text = district.name;
          opt.dataset.code = district.code;  // Lưu mã code vào data attribute

          // Nếu tên quận/huyện trùng với giá trị hiện tại, chọn option này
          if (district.name === selectedDistrict) {
            opt.selected = true;
            // Tải xã/phường cho quận/huyện này
            setTimeout(() => {
              loadWards(district.code, selectedWard);
            }, 100);
          }

          districtSelect.add(opt);
        });
        districtSelect.disabled = false;
      });
  }

  // Hàm tải xã/phường
  function loadWards(districtCode, selectedWard) {
    fetch(`https://provinces.open-api.vn/api/d/${districtCode}?depth=2`)
      .then((res) => res.json())
      .then((data) => {
        // Lưu lại option đầu tiên
        const defaultOption = wardSelect.options[0];
        wardSelect.innerHTML = '';
        wardSelect.add(defaultOption);

        data.wards.forEach((ward) => {
          const opt = document.createElement("option");
          opt.value = ward.name;  // Lưu tên thay vì mã code
          opt.text = ward.name;
          opt.dataset.code = ward.code;  // Lưu mã code vào data attribute

          // Nếu tên xã/phường trùng với giá trị hiện tại, chọn option này
          if (ward.name === selectedWard) {
            opt.selected = true;
          }

          wardSelect.add(opt);
        });
        wardSelect.disabled = false;
      });
  }

  // Tải quận/huyện khi tỉnh được chọn
  provinceSelect.addEventListener("change", () => {
    const selectedOption = provinceSelect.options[provinceSelect.selectedIndex];
    const provinceName = selectedOption.value;
    let provinceCode = selectedOption.dataset.code;  // Lấy mã code từ data attribute

    districtSelect.innerHTML = '<option value="">Chọn Quận/Huyện</option>';
    wardSelect.innerHTML = '<option value="">Chọn Xã/Phường</option>';
    wardSelect.disabled = true;

    if (provinceCode) {
      loadDistricts(provinceCode);
    } else if (provinceName) {
      // Nếu không có mã code, tìm mã code từ API
      fetch("https://provinces.open-api.vn/api/?depth=1")
        .then((res) => res.json())
        .then((data) => {
          const province = data.find(p => p.name === provinceName);
          if (province) {
            // Lưu mã code vào option để sử dụng sau này
            selectedOption.dataset.code = province.code;
            loadDistricts(province.code);
          }
        });
    }
  });

  // tải xã/phường khi quận/huyện được chọn
  districtSelect.addEventListener("change", () => {
    const selectedOption = districtSelect.options[districtSelect.selectedIndex];
    const districtName = selectedOption.value;
    let districtCode = selectedOption.dataset.code;  // Lấy mã code từ data attribute

    wardSelect.innerHTML = '<option value="">Chọn Xã/Phường</option>';
    wardSelect.disabled = true;

    if (districtCode) {
      loadWards(districtCode);
    } else if (districtName && provinceSelect.value) {
      // Nếu không có mã code, tìm mã code từ API
      const provinceOption = provinceSelect.options[provinceSelect.selectedIndex];
      const provinceCode = provinceOption.dataset.code;

      if (provinceCode) {
        fetch(`https://provinces.open-api.vn/api/p/${provinceCode}?depth=2`)
          .then((res) => res.json())
          .then((data) => {
            const district = data.districts.find(d => d.name === districtName);
            if (district) {
              // Lưu mã code vào option để sử dụng sau này
              selectedOption.dataset.code = district.code;
              loadWards(district.code);
            }
          });
      }
    }
  });

  // Xử lý form trước khi gửi
  const addressForm = document.getElementById('address-form');
  if (addressForm) {
    addressForm.addEventListener('submit', function(event) {
      // Kiểm tra xem các trường bắt buộc đã được điền chưa
      const city = provinceSelect.value;
      const district = districtSelect.value;
      const ward = wardSelect.value;

      console.log('Dữ liệu form trước khi gửi:', {
        city,
        district,
        ward
      });

      if (!city || !district || !ward) {
        event.preventDefault();
        alert('Vui lòng chọn đầy đủ Tỉnh/Thành phố, Quận/Huyện và Xã/Phường');
        return false;
      }

      // Đảm bảo rằng các trường select không bị disabled khi gửi form
      provinceSelect.disabled = false;
      districtSelect.disabled = false;
      wardSelect.disabled = false;

      return true;
    });
  }
});

