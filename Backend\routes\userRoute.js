const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/authMiddleware');
const { getUserProfile, updateUser, updateAddress } = require('../controllers/userController');
const { getMyOrders, cancelOrder } = require('../controllers/orderController');
const { getReviewById } = require('../controllers/reviewController');
const Category = require('../models/categoryModel');

router.get('/customer', auth, async (req, res) => {
  const user = await getUserProfile(req, res, true);
  const categories = await Category.find().sort({ name: 1 });
  if (user) {
    res.render('customer/customer', { user, categories });
  }
});

router.get('/customer/address', auth, async (req, res) => {
  const user = await getUserProfile(req, res, true);
  const categories = await Category.find().sort({ name: 1 });
  if (user) {
    res.render('customer/customer-addr', { user, categories });
  }
});

router.get('/customer/change-password', auth, async (req, res) => {
  const categories = await Category.find().sort({ name: 1 });
  res.render('customer/change-key', { categories });
});

router.get('/customer/orders', auth, getMyOrders);

router.get('/customer/orders/check', auth, (req, res) => {
  req.query.status = 'pending';
  getMyOrders(req, res);
});
router.get('/customer/orders/transport', auth, (req, res) => {
  req.query.status = 'transporting';
  getMyOrders(req, res);
});
router.get('/customer/orders/receive', auth, (req, res) => {
  req.query.status = 'delivered';
  getMyOrders(req, res);
});
router.get('/customer/orders/cancel', auth, (req, res) => {
  req.query.status = 'cancelled';
  getMyOrders(req, res);
});

router.post('/customer/update', auth, async (req, res) => {
  const success = await updateUser(req, res);
  if (success) {
    res.redirect('/customer');
  } else {
    res.status(500).send('Lỗi cập nhật');
  }
});

router.post('/customer/address/update', auth, updateAddress);

// Route hủy đơn hàng
router.post('/customer/orders/:orderId/cancel', auth, cancelOrder);

// Route xem đánh giá
router.get('/reviews/:reviewId', auth, getReviewById);

// Debug route
router.get('/debug/reviews', auth, async (req, res) => {
  try {
    const Review = require('../models/reviewModel');
    const Order = require('../models/orderModel');

    const reviews = await Review.find({ user: req.user._id })
      .populate('book', 'title')
      .populate('order', '_id createdAt')
      .lean();

    const orders = await Order.find({ user: req.user._id, status: 'delivered' })
      .lean();

    res.json({
      userId: req.user._id,
      reviews: reviews,
      orders: orders.map(order => ({
        id: order._id,
        createdAt: order.createdAt,
        hasReview: reviews.some(review => review.order && review.order._id.toString() === order._id.toString())
      }))
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;