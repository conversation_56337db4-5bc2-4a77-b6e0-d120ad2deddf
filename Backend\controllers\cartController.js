const Book = require('../models/bookModel');
const Cart = require('../models/cartModel');
const Category = require('../models/categoryModel');

const getCart = async function(req, res) {
  try {
    let cartItems = [];
    let cart = [];
    let total = 0;
    if (req.user) {
      // Optimize by selecting only needed fields and using lean() for better performance
      const userCart = await Cart.findOne({ userId: req.user._id })
        .populate('items.bookId', 'title price image _id stock provider')
        .lean();

      if (userCart && userCart.items.length > 0) {
        cartItems = userCart.items;
        cart = userCart.items.map(item => ({
          product: item.bookId,
          productId: item.bookId._id,
          quantity: item.quantity,
          price: item.bookId.price
        }));

        total = userCart.items.reduce((sum, item) => {
          const price = item.bookId?.price || 0;
          return sum + (item.quantity * price);
        }, 0);

        total = parseFloat(total.toFixed(2));
      }
    } else {
      cartItems = req.session.cart || [];
      cart = req.session.cart ? req.session.cart.map(item => ({
        product: {
          _id: item.productId,
          title: item.title,
          image: item.image,
          price: item.price,
          provider: 'Không rõ',
          stock: 'N/A'
        },
        productId: item.productId,
        quantity: item.quantity,
        price: item.price,
        img: item.image,
        title: item.title
      })) : [];

      // Calculate total with error handling
      total = cartItems.reduce((sum, item) => {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 0;
        return sum + (price * quantity);
      }, 0);

      // Format total to 2 decimal places
      total = parseFloat(total.toFixed(2));
    }

    console.log('Render cart với:', {
      cartItemsCount: cartItems.length,
      cartCount: cart.length,
      total
    });

    // Lấy categories cho header
    const categories = await Category.find().sort({ name: 1 });

    res.render('cart/cart', {
      cartItems,
      cart,
      total,
      user: req.user,
      categories: categories
    });
  } catch (err) {
    console.error('Error getting cart:', err);
    res.status(500).json({ error: 'Lỗi khi lấy giỏ hàng' });
  }
};

const addToCart = async function(req, res) {
  const bookId = req.params.id;
  const quantity = parseInt(req.body.quantity) || 1;

  try {
    // Only select the fields we need + stock for inventory check
    const book = await Book.findById(bookId)
      .select('_id title price image stock')
      .lean();

    if (!book) return res.status(404).json({
      success: false,
      message: 'Sách không tồn tại'
    });

    // Kiểm tra sách đã hết hàng
    if (book.stock <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Sản phẩm đã hết hàng',
        outOfStock: true
      });
    }

    // Kiểm tra số lượng hiện có trong giỏ hàng
    let existingQuantity = 0;
    if (req.user) {
      const existingCart = await Cart.findOne({
        userId: req.user._id,
        'items.bookId': bookId
      });
      if (existingCart) {
        const existingItem = existingCart.items.find(item =>
          item.bookId.toString() === bookId
        );
        existingQuantity = existingItem ? existingItem.quantity : 0;
      }
    } else {
      const sessionCart = req.session.cart || [];
      const existingItem = sessionCart.find(i => i.productId == bookId);
      existingQuantity = existingItem ? existingItem.quantity : 0;
    }

    // Kiểm tra tổng số lượng (hiện có + muốn thêm)
    const totalQuantity = existingQuantity + quantity;
    if (totalQuantity > book.stock) {
      const availableToAdd = book.stock - existingQuantity;
      if (availableToAdd <= 0) {
        return res.status(400).json({
          success: false,
          message: `Bạn đã có ${existingQuantity} sản phẩm trong giỏ hàng. Chỉ còn ${book.stock} sản phẩm trong kho`,
          availableStock: book.stock,
          currentInCart: existingQuantity,
          canAdd: 0
        });
      } else {
        return res.status(400).json({
          success: false,
          message: `Bạn đã có ${existingQuantity} sản phẩm trong giỏ hàng. Chỉ có thể thêm tối đa ${availableToAdd} sản phẩm nữa`,
          availableStock: book.stock,
          currentInCart: existingQuantity,
          canAdd: availableToAdd
        });
      }
    }



    console.log('Thêm sách vào giỏ hàng:', {
      bookId: book._id,
      title: book.title,
      price: book.price,
      quantity
    });

    if (req.user) {
      // Use findOneAndUpdate for atomic operation (better performance)
      const result = await Cart.findOneAndUpdate(
        { userId: req.user._id, 'items.bookId': bookId },
        { $inc: { 'items.$.quantity': quantity } },
        { new: true }
      );

      // If the item wasn't in the cart yet
      if (!result) {
        await Cart.findOneAndUpdate(
          { userId: req.user._id },
          {
            $push: { items: { bookId, quantity } },
            $setOnInsert: { userId: req.user._id }
          },
          { upsert: true, new: true }
        );
      }

      // Kiểm tra giỏ hàng sau khi thêm
      const updatedCart = await Cart.findOne({ userId: req.user._id })
        .populate('items.bookId', 'title price image _id')
        .lean();

      console.log('Giỏ hàng sau khi thêm:', updatedCart ?
        `${updatedCart.items.length} sản phẩm` : 'Không có giỏ hàng');

      return res.status(200).json({
        success: true,
        message: 'Đã thêm vào giỏ hàng',
        itemCount: quantity
      });
    } else {
      // Session cart for non-logged in users
      if (!req.session.cart) req.session.cart = [];

      const existing = req.session.cart.find(i => i.productId == bookId);
      if (existing) {
        existing.quantity += quantity;
      } else {
        req.session.cart.push({
          productId: book._id,
          title: book.title,
          price: book.price,
          quantity,
          image: book.image
        });
      }

      console.log('Giỏ hàng session sau khi thêm:',
        req.session.cart ? `${req.session.cart.length} sản phẩm` : 'Không có giỏ hàng');

      return res.status(200).json({
        success: true,
        message: 'Đã thêm vào giỏ hàng',
        itemCount: quantity
      });
    }
  } catch (err) {
    console.error('Error adding to cart:', err);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi thêm vào giỏ hàng'
    });
  }
};

const increaseQuantity = async function(req, res) {
  const bookId = req.params.id;
  try {
    // Kiểm tra stock của sản phẩm trước
    const book = await Book.findById(bookId).select('stock title').lean();
    if (!book) {
      return res.status(404).json({
        success: false,
        message: 'Sản phẩm không tồn tại'
      });
    }

    if (req.user) {
      // Tìm cart item hiện tại để kiểm tra quantity
      const cart = await Cart.findOne({
        userId: req.user._id,
        'items.bookId': bookId
      });

      if (cart) {
        const item = cart.items.find(item => item.bookId.toString() === bookId);
        if (item) {
          // Kiểm tra xem có thể tăng thêm không
          if (item.quantity >= book.stock) {
            return res.status(400).json({
              success: false,
              message: `Chỉ còn ${book.stock} sản phẩm trong kho`,
              availableStock: book.stock,
              currentQuantity: item.quantity
            });
          }

          // Tăng số lượng
          await Cart.findOneAndUpdate(
            { userId: req.user._id, 'items.bookId': bookId },
            { $inc: { 'items.$.quantity': 1 } }
          );
        }
      }
    } else {
      const cart = req.session.cart || [];
      const item = cart.find(i => i.productId == bookId);
      if (item) {
        // Kiểm tra stock cho session cart
        if (item.quantity >= book.stock) {
          return res.status(400).json({
            success: false,
            message: `Chỉ còn ${book.stock} sản phẩm trong kho`,
            availableStock: book.stock,
            currentQuantity: item.quantity
          });
        }
        item.quantity += 1;
        req.session.cart = cart;
      }
    }

    res.status(200).json({ success: true });
  } catch (err) {
    console.error('Error increasing quantity:', err);
    res.status(500).json({ success: false, message: 'Lỗi server' });
  }
};

const decreaseQuantity = async function(req, res) {
  const bookId = req.params.id;
  try {
    if (req.user) {
      // Tìm cart item hiện tại để kiểm tra quantity
      const cart = await Cart.findOne({
        userId: req.user._id,
        'items.bookId': bookId
      });

      if (cart) {
        const item = cart.items.find(item => item.bookId.toString() === bookId);
        if (item && item.quantity > 1) {
          // Chỉ giảm nếu quantity > 1
          await Cart.findOneAndUpdate(
            {
              userId: req.user._id,
              'items.bookId': bookId
            },
            { $inc: { 'items.$.quantity': -1 } }
          );
        } else {
          return res.status(400).json({
            success: false,
            message: 'Không thể giảm số lượng xuống dưới 1'
          });
        }
      }
    } else {
      const cart = req.session.cart || [];
      const item = cart.find(i => i.productId == bookId);
      if (item && item.quantity > 1) {
        item.quantity -= 1;
        req.session.cart = cart;
      } else {
        return res.status(400).json({
          success: false,
          message: 'Không thể giảm số lượng xuống dưới 1'
        });
      }
    }

    res.status(200).json({ success: true });
  } catch (err) {
    console.error('Error decreasing quantity:', err);
    res.status(500).json({ success: false, message: 'Lỗi server' });
  }
};

const removeFromCart = async (req, res) => {
  const bookId = req.params.id;
  try {
    if (req.user) {
      // Use atomic update operation to remove item
      await Cart.findOneAndUpdate(
        { userId: req.user._id },
        { $pull: { items: { bookId: bookId } } }
      );
    } else {
      // For session cart
      let cart = req.session.cart || [];
      cart = cart.filter(i => i.productId != bookId);
      req.session.cart = cart;
    }

    res.status(200).json({ success: true });
  } catch (err) {
    console.error('Error removing from cart:', err);
    res.status(500).json({ success: false, message: 'Lỗi server' });
  }
};

const checkout = async function(req, res) {
  try {
    let cartItems = [];
    let total = 0;

    if (req.user) {
      // For logged in users - optimize query
      const cart = await Cart.findOne({ userId: req.user._id })
        .populate('items.bookId', 'title image price _id')
        .lean();

      console.log('Giỏ hàng trong checkout:', cart ?
        `Tìm thấy giỏ hàng với ${cart.items.length} sản phẩm` : 'Không tìm thấy giỏ hàng');

      if (!cart || cart.items.length === 0) {
        return res.redirect('/cart');
      }

      // Transform cart items for rendering
      cartItems = cart.items.map(i => ({
        book: {
          _id: i.bookId._id,
          title: i.bookId.title,
          image: i.bookId.image,
          price: i.bookId.price
        },
        quantity: i.quantity,
        price: i.bookId.price
      }));

      console.log('Đã chuyển đổi cartItems:',
        cartItems.length > 0 ? `${cartItems.length} sản phẩm, sản phẩm đầu tiên: ${cartItems[0].book.title}` : 'Không có sản phẩm');

      // Calculate total with error handling
      total = cartItems.reduce((sum, item) => {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 0;
        return sum + (price * quantity);
      }, 0);

      // Format total to 2 decimal places
      total = parseFloat(total.toFixed(2));

      // User address is available in req.user
    } else {
      // For non-logged in users
      const sessionCart = req.session.cart || [];

      console.log('Giỏ hàng session trong checkout:',
        sessionCart.length > 0 ? `${sessionCart.length} sản phẩm` : 'Không có sản phẩm');

      if (sessionCart.length === 0) {
        return res.redirect('/cart');
      }
      cartItems = sessionCart.map(item => ({
        book: {
          _id: item.productId,
          title: item.title,
          image: item.image,
          price: item.price
        },
        quantity: item.quantity,
        price: item.price
      }));
      total = cartItems.reduce((sum, item) => {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 0;
        return sum + (price * quantity);
      }, 0);
      total = parseFloat(total.toFixed(2));
    }
    console.log('Render checkout với:', {
      cartItemsCount: cartItems.length,
      total,
      hasUser: !!req.user
    });
    const categories = await Category.find().sort({ name: 1 });
    res.render('cart/checkout', {
      cartItems,
      cart: cartItems,
      total,
      user: req.user,
      categories: categories
    });
  } catch (err) {
    console.error('Error in checkout:', err);
    res.redirect('/cart');
  }
};

module.exports = {
  getCart,
  addToCart,
  increaseQuantity,
  decreaseQuantity,
  removeFromCart,
  checkout
};
