@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
header{
    font-family: 'Roboto', sans-serif;
}
.header{
    width: 100%;
    height: auto;
    text-align: center;
    background-color:#c12530 ;
}
.menu{


    padding:20px;
    height: 60px;
    background-color: white;

}
.menu ul{
    display: flex;
    justify-content: space-around;
}
.menu ul li a:hover{
    background:linear-gradient(to right, #e03a3a, #ec7e7e);
    color: white;
    transition: 0.8s;
    border-radius: 15px;
}
.menu ul li{
    list-style-type: none;



}
.menu ul li a{
    text-align: center;
    color: black;
    padding:21px;

}

.menu ul li input{
    width: 555px;
    height: 32px;
    border-radius: 5px;
    font-size: 15px;
    padding-left: 20px;



}
.menu ul li:hover {
    color: black;
}
.menu ul li .fa-magnifying-glass {
    background-color: #e04852;
    width: 70px;
    height: 27px;
    padding: 6px;
    color: white;
    font-size: 20px;
    margin-left: -77px;
    border-radius: 5px;
    text-align: center;
    top: 86px;
    position: absolute;

}
.menu ul li i{
    font-size: 18px;
    color: black;




}
.menu .sub-menu{
    display:none ;
    position: absolute;
    flex-direction: column;
    top: 124px;
    background-color: white;
    box-shadow: 0 0 10px darkgrey;
    border-radius: 8px;
    z-index: 1;
}

.menu .sub-menu li {
    list-style-type: none;
    padding:  20px;
    text-align: center;
    font-size: 14px;
    color: black;

}
.menu .sub-menu li a{
    text-decoration: none;
    color: black;
    padding: 10px 30px;
}
.menu li:hover .sub-menu  {
    display: flex;

}


.anh {
    width: 1220px;
    overflow: hidden;
    margin: 20px auto;
    position: relative;
}

.anh2 {
    display: flex;
    width: calc(1220px * 3); /* số ảnh * chiều rộng ảnh */
    transition: transform 0.5s ease-in-out;

}

.anh2 img {
    width: 1220px;
    border-radius: 20px;
}
.prev-btn,
.next-btn {
    position: absolute;
    top: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 10px;
    cursor: pointer;
    font-size: 24px;


}

.prev-btn {
    left: 20px;
}

.next-btn {
    right: 20px;
}

.prev-btn:hover,
.next-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
}




/*** silder ***/




.silder{
    font-family: 'Roboto', sans-serif;
}
 h1{
    text-align: center;
    font-size: 30px;
    padding: 10px;


}
.banchay{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    background: linear-gradient(to right, #c03d46, #f0f0f0);
    border-radius: 8px;
    margin-bottom: 50px;
    padding: 20px;
    align-items: start;
    justify-items: center;
}
.banchay h1{
    display: none;
}
.banchay .banchay1{
    width: 100%;
    text-align: center;
    padding: 15px 10px;
    max-width: 200px;
    margin: 0 auto;
}

.banchay .banchay1 a {
    text-decoration: none;
    color: inherit;
    display: block;
}

.banchay .banchay1 img{
    width: 100%;
    max-width: 150px;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.banchay .banchay1 img:hover {
    transform: scale(1.05);
}

.banchay .banchay1 h3 {
    font-size: 14px;
    margin: 8px 0 4px 0;
    color: #333;
    font-weight: 600;
}

.banchay .banchay1 p {
    font-size: 13px;
    color: #666;
    margin: 0;
    line-height: 1.3;
    height: 35px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

h2{
    position: relative;
    background:linear-gradient(to right, #f9eaea, #db8b8b);
    color: black;
    padding:20px 0 ;
    border-radius: 8px;
    text-align: left;
    padding-left: 50px;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
}
h2 i{
    display: none;
}

.slider2, .slider3 {
    margin-bottom: 40px;
}

.slider3 h2 {
    background: linear-gradient(to right, #ff6b6b, #ffa8a8);
}

.slider2 h2 {
    background: linear-gradient(to right, #ff6b6b, #ffa8a8);
}

a{
    text-decoration: none;
}









/***** footer *****/
footer{
    width: 100%;
    height: 300px;
    background:linear-gradient(to top, #303030, #676565);
    color: white;
    border-radius: 8px;
}
.footer{
    display: flex;
    justify-content: space-around;
    align-items: center;
}



.footer .chantrang h3{
    font-size: 30px;
    padding: 30px 0;
    text-align: center;
}



.footer .chantrang ul li a{
    text-decoration: none;
    color: white;
    list-style-type: none;
    font-size: 18px;
    padding: 14px 0;

}
.footer .chantrang ul li a:hover,
 .footer .chantrang:hover h3{
    color: gold;
}



.xuhuong{
    width: 100%;
    height: 100%;

}
.xuhuong_hot{

    display: flex;
    align-items: center;
    height: 40px;
    box-shadow: 0 0 5px rgba(0,0,0,0.5);

}
.xuhuong_hot p{
    font-size: 18px;
    padding: 0 20px;
}
.xuhuong_hot p:hover{
    color: red;
    border-bottom: 2px solid red;
}

/* Responsive Design */
@media (max-width: 768px) {
    .banchay {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
        padding: 15px;
    }

    .banchay .banchay1 {
        max-width: 150px;
        padding: 10px 5px;
    }

    .banchay .banchay1 img {
        max-width: 120px;
        height: 160px;
    }

    .banchay .banchay1 h3 {
        font-size: 12px;
    }

    .banchay .banchay1 p {
        font-size: 11px;
        height: 30px;
    }
}

@media (max-width: 480px) {
    .banchay {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        padding: 10px;
    }

    .banchay .banchay1 {
        max-width: 130px;
    }

    .banchay .banchay1 img {
        max-width: 100px;
        height: 140px;
    }
}
