const Cart = require('../models/cartModel');
const User = require('../models/userModel');

const checkoutPage = async function(req, res) {
  if (!req.user) return res.redirect('/login');

  try {
    const cart = await Cart.findOne({ userId: req.user._id }).populate('items.bookId');
    if (!cart || cart.items.length === 0) return res.redirect('/cart');

    const cartItems = cart.items.map(i => ({
      book: i.bookId,
      quantity: i.quantity,
      price: i.bookId.price,
    }));

    const total = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const user = await User.findById(req.user._id).lean();

    console.log('Rendering checkout with:', {
      cartItems: cartItems.length,
      total,
      user: user ? 'User found' : 'No user'
    });

    res.render('cart/checkout', {
      cart: cartItems,
      total,
      user
    });
  } catch (err) {
    console.error('Error in checkout page:', err);
    res.redirect('/cart');
  }
};

const checkoutPageWithSelected = async function(req, res) {
  if (!req.user) return res.redirect('/login');

  try {
    const { selectedBooks } = req.body;
    console.log('Sách được chọn từ cart:', selectedBooks);

    if (!selectedBooks || selectedBooks.length === 0) {
      return res.redirect('/cart');
    }

    const cart = await Cart.findOne({ userId: req.user._id }).populate('items.bookId');
    if (!cart || cart.items.length === 0) return res.redirect('/cart');

    // Lọc chỉ những sản phẩm được chọn
    const selectedBookIds = Array.isArray(selectedBooks) ? selectedBooks : [selectedBooks];
    const selectedItems = cart.items.filter(item =>
      selectedBookIds.includes(item.bookId._id.toString())
    );

    if (selectedItems.length === 0) {
      return res.redirect('/cart');
    }

    const cartItems = selectedItems.map(i => ({
      book: i.bookId,
      quantity: i.quantity,
      price: i.bookId.price,
    }));

    const total = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const user = await User.findById(req.user._id).lean();

    console.log('Rendering checkout với sách được chọn:', {
      selectedItems: cartItems.length,
      total,
      bookTitles: cartItems.map(item => item.book.title)
    });

    res.render('cart/checkout', {
      cart: cartItems,
      total,
      user
    });
  } catch (err) {
    console.error('Error in checkout page with selected:', err);
    res.redirect('/cart');
  }
};

module.exports = { checkoutPage, checkoutPageWithSelected };
