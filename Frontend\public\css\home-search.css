#search-results {
  min-height: 60vh;
  padding: 20px 0;
}

.search-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 10px;
}

.search-header h1 {
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
}

.search-header p {
  color: #666;
  font-size: 16px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.product-item {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
}

.product-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.product-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.out-of-stock-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #dc3545;
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
}

.product-info {
  padding: 15px;
}

.product-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-author {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.product-price {
  margin-bottom: 10px;
}

.price {
  font-size: 18px;
  font-weight: bold;
  color: #c92127;
}

.product-stats {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #888;
}

.stock.out-of-stock {
  color: #dc3545;
  font-weight: bold;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 30px;
}

.page-btn {
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  text-decoration: none;
  color: #333;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.page-btn:hover {
  background-color: #c92127;
  color: white;
  border-color: #c92127;
}

.page-btn.active {
  background-color: #c92127;
  color: white;
  border-color: #c92127;
}

.page-btn.dots {
  border: none;
  cursor: default;
}

.page-btn.dots:hover {
  background-color: transparent;
  color: #333;
}

.no-results {
  text-align: center;
  padding: 60px 20px;
}

.no-results-content i {
  font-size: 80px;
  color: #ddd;
  margin-bottom: 20px;
}

.no-results h2 {
  color: #333;
  margin-bottom: 15px;
}

.no-results p {
  color: #666;
  margin-bottom: 30px;
  font-size: 16px;
}

.suggestions {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin: 20px auto;
  max-width: 400px;
}

.suggestions h3 {
  color: #333;
  margin-bottom: 15px;
}

.suggestions ul {
  list-style: none;
  padding: 0;
}

.suggestions li {
  padding: 5px 0;
  color: #666;
}

.suggestions li:before {
  content: "• ";
  color: #c92127;
  font-weight: bold;
}

.back-home {
  display: inline-block;
  padding: 12px 30px;
  background-color: #c92127;
  color: white;
  text-decoration: none;
  border-radius: 25px;
  transition: background-color 0.3s ease;
  margin-top: 20px;
}

.back-home:hover {
  background-color: #a01e24;
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }

  .search-header h1 {
    font-size: 24px;
  }

  .pagination {
    flex-wrap: wrap;
    gap: 5px;
  }

  .page-btn {
    padding: 8px 12px;
    font-size: 14px;
  }
}

/* === Search Suggestions Dropdown === */

.search-suggestions.show {
  display: block !important;
}

.suggestion-item {
  padding: 12px 15px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 10px;
}

.suggestion-item:hover,
.suggestion-item.active {
  background-color: #f8f9fa;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.book-suggestion {
  padding: 10px 15px;
  gap: 12px;
}

.book-image {
  width: 50px;
  height: 60px;
  flex-shrink: 0;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.book-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.book-info {
  flex: 1;
  min-width: 0;
}

.book-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.book-author {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.book-price {
  font-size: 13px;
  font-weight: 600;
  color: #c92127;
  margin-bottom: 2px;
}

.match-type {
  font-size: 11px;
  color: #888;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 10px;
  display: inline-block;
}
