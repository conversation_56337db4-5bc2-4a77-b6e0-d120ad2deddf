const mongoose = require('mongoose');

const connectDB = async function() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Kết nối MongoDB thành công');
    if (process.env.NODE_ENV === 'development') {
      const admin = new mongoose.mongo.Admin(mongoose.connection.db);
    }
  } catch (err) {
    console.error('Kết nối MongoDB thất bại', err);
    process.exit(1);
  }
};

module.exports = connectDB;
