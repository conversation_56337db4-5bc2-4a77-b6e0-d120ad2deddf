doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title ADMIN - Quản lí đơn hàng
    link(rel='stylesheet' href='/public/css/admin.css')
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css')
  body
    header
      .content
        ul.logo
          li
            h3 WEB BÁN SÁCH TRUYỆN NHÓM 9 - ADMIN
          li
            a(href='/auth/logout')
              | Đăng xuất
              i.fa-solid.fa-right-from-bracket
        ul.quanli
          li
            a(href='/admin')
              i.fa-solid.fa-house
              h4 Quản lí thông tin tổng quát
          li
            a(href='/admin/user')
              i.fa-solid.fa-circle-user
              h4 Quản lí người dùng
          li
            a(href='/admin/book')
              i.fa-solid.fa-book
              h4 Quản lí sách
          li
            a(href='/admin/category')
              i.fa-solid.fa-address-book
              h4 Quản lí danh mục
          li
            a(href='/admin/order')
              i.fa-solid.fa-cart-shopping
              h4 Quản lí đơn hàng
          li
            a(href='/admin/sale')
              i.fa-solid.fa-sack-dollar
              h4 Quản lí doanh thu
          li
            a(href='/')
              i.fa-solid.fa-share-from-square
              h4 Quay về trang chủ
    .main
      .admin
        h2 Quản lí đơn hàng
    .donhang
      .danhsachdonhang
        h3 QUẢN LÍ ĐƠN HÀNG
        table.thongtindonhang
          tr
            th STT
            th Mã đơn
            th Khách hàng
            th ngày đặt
            th trạng thái
            th Thao tác
          if orders && orders.length > 0
            each order, index in orders
              tr
                td= index + 1
                td= order._id
                td= order.user ? order.user.username : 'Không có thông tin'
                td= new Date(order.createdAt).toLocaleDateString('vi-VN')
                td
                  span(class=`status-${order.status}`)
                    if order.status === 'pending'
                      | Chờ xác nhận
                    else if order.status === 'transporting'
                      | Đang giao hàng
                    else if order.status === 'delivered'
                      | Đã giao thành công
                    else if order.status === 'cancelled'
                      | Đã hủy
                    else
                      = order.status
                td
                  button.btn2(onclick=`viewOrder('${order._id}')`)
                    i.fa-solid.fa-eye
          else
            tr
              td(colspan="6" style="text-align: center; padding: 20px;") Không có dữ liệu đơn hàng
    script(src='/public/js/admin.js')
