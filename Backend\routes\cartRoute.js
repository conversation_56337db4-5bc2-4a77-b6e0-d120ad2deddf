const router = require('express').Router();
const cartController = require('../controllers/cartController');

// Middleware để load user info nhưng không bắt buộc đăng nhập
const loadUser = async (req, res, next) => {
  try {
    const User = require('../models/userModel');
    const jwt = require('jsonwebtoken');

    const token = req.cookies.token;
    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id).select("-password");
      if (user) {
        req.user = user;
        res.locals.user = user;
      }
    }
    next();
  } catch (error) {
    // Không redirect, chỉ tiếp tục mà không có user
    next();
  }
};

router.use(loadUser);

router.get('/', cartController.getCart);
router.post('/add/:id', cartController.addToCart);
router.post('/increase/:id', cartController.increaseQuantity);
router.post('/decrease/:id', cartController.decreaseQuantity);
router.post('/remove/:id', cartController.removeFromCart);
router.get('/checkout', cartController.checkout);

module.exports = router;
