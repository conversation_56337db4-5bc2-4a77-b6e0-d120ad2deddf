doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title ADMIN - Chi tiết đơn hàng
    link(rel='stylesheet' href='/public/css/admin.css')
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css')
  body
    header
      .content
        ul.logo
          li
            h3 WEB BÁN SÁCH TRUYỆN NHÓM 9 - ADMIN
          li
            a(href='/auth/logout')
              | Đăng xuất
              i.fa-solid.fa-right-from-bracket
        ul.quanli
          li
            a(href='/admin')
              i.fa-solid.fa-house
              h4 Quản lí thông tin tổng quát
          li
            a(href='/admin/user')
              i.fa-solid.fa-circle-user
              h4 Quản lí người dùng
          li
            a(href='/admin/book')
              i.fa-solid.fa-book
              h4 Quản lí sách
          li
            a(href='/admin/category')
              i.fa-solid.fa-address-book
              h4 Quản lí danh mục
          li
            a(href='/admin/order')
              i.fa-solid.fa-cart-shopping
              h4 Quản lí đơn hàng
          li
            a(href='/admin/sale')
              i.fa-solid.fa-sack-dollar
              h4 Quản lí doanh thu
          li
            a(href='/')
              i.fa-solid.fa-share-from-square
              h4 Quay về trang chủ
    .main
      .admin
        h2 Chi tiết đơn hàng
    .chitietdonhang
      .thongtindonhang
        h3 THÔNG TIN ĐƠN HÀNG
        .order-info
          .info-row
            span.label Mã đơn hàng:
            span.value= order._id
          .info-row
            span.label Khách hàng:
            span.value= order.user ? order.user.username : 'Không có thông tin'
          .info-row
            span.label Email:
            span.value= order.user ? order.user.email : 'Không có thông tin'
          .info-row
            span.label Ngày đặt:
            span.value= new Date(order.createdAt).toLocaleDateString('vi-VN')
          .info-row
            span.label Trạng thái:
            span.value(class=`status-${order.status}`)= order.status
          .info-row
            span.label Địa chỉ giao hàng:
            span.value= order.shippingAddress || 'Không có thông tin'
          .info-row
            span.label Tổng tiền:
            span.value.total-price= order.totalPrice.toLocaleString('vi-VN') + ' đ'

      .danhsachsanpham
        h3 DANH SÁCH SẢN PHẨM
        table.chitiet-sanpham
          tr
            th STT
            th Ảnh
            th Tên sách
            th Số lượng
            th Đơn giá
            th Thành tiền
          if order.items && order.items.length > 0
            each item, index in order.items
              tr
                td= index + 1
                td
                  if item.book && item.book.image
                    img(src=item.book.image alt=item.book.title style='width: 50px; height: auto;')
                  else
                    span Không có ảnh
                td= item.book ? item.book.title : 'Sách đã bị xóa'
                td= item.quantity
                td= item.price.toLocaleString('vi-VN') + ' đ'
                td= (item.quantity * item.price).toLocaleString('vi-VN') + ' đ'
          else
            tr
              td(colspan="6" style="text-align: center; padding: 20px;") Không có sản phẩm trong đơn hàng

      .capnhatdonhang
        h3 CẬP NHẬT TRẠNG THÁI
        form(action=`/admin/order/update-status/${order._id}` method="POST")
          .form-group
            label(for='status') Trạng thái:
            select#status(name='status' required)
              option(value='pending' selected=(order.status === 'pending')) Chờ xác nhận
              option(value='transporting' selected=(order.status === 'transporting')) Đang giao hàng
              option(value='delivered' selected=(order.status === 'delivered')) Đã giao thành công
              option(value='cancelled' selected=(order.status === 'cancelled')) Đã hủy
          .button-group
            button.btn-back(type='button' onclick="window.location.href='/admin/order'") Quay lại
            button.btn-save(type='submit') Cập nhật trạng thái
            if order.status !== 'cancelled' && order.status !== 'delivered'
              button.btn-cancel(type='button' onclick=`cancelOrder('${order._id}')`) Hủy đơn hàng
    script(src='/public/js/admin.js')
