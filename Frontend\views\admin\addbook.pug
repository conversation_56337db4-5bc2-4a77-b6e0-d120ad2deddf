doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title ADMIN - Thêm sách mới
    link(rel='stylesheet' href='/public/css/admin.css')
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css')
  body
    header
      .content
        ul.logo
          li
            h3 WEB BÁN SÁCH TRUYỆN NHÓM 9 - ADMIN
          li
            a(href='/auth/logout')
              | Đăng xuất
              i.fa-solid.fa-right-from-bracket
        ul.quanli
          li
            a(href='/admin')
              i.fa-solid.fa-house
              h4 Quản lí thông tin tổng quát
          li
            a(href='/admin/user')
              i.fa-solid.fa-circle-user
              h4 Quản lí người dùng
          li
            a(href='/admin/book')
              i.fa-solid.fa-book
              h4 Quản lí sách
          li
            a(href='/admin/category')
              i.fa-solid.fa-address-book
              h4 Quản lí danh mục
          li
            a(href='/admin/order')
              i.fa-solid.fa-cart-shopping
              h4 Quản lí đơn hàng
          li
            a(href='/admin/sale')
              i.fa-solid.fa-sack-dollar
              h4 Quản lí doanh thu
          li
            a(href='/')
              i.fa-solid.fa-share-from-square
              h4 Quay về trang chủ
    .main
      .admin
        h2 Thêm sách mới
    .capnhapsachmoi
      .form-container
        h2 Thêm sách mới
        form(action="/admin/book/add" method="POST" enctype="multipart/form-data")
          .form-columns
            .form-column
              .form-group
                label(for='tensach') Tên sách:
                input#tensach(type='text' name='title' placeholder='Nhập tên sách' required)
              .form-group
                label(for='tentacgia') Tác giả:
                input#tentacgia(type='text' name='author' placeholder='Nhập tên tác giả' required)
              .form-group
                label(for='nhaxuatban') Nhà xuất bản:
                input#nhaxuatban(type='text' name='publisher' placeholder='Nhập tên nhà xuất bản')
              .form-group
                label(for='nhacungcap') Nhà cung cấp:
                input#nhacungcap(type='text' name='supplier' placeholder='Nhập tên nhà cung cấp')
              .form-group
                label(for='namxuatban') Năm xuất bản:
                input#namxuatban(type='number' name='year' min='1900' max='2030' placeholder='Năm xuất bản')
              .form-group
                label(for='ngonngu') Ngôn ngữ:
                select#ngonngu(name='bookLanguage')
                  option(value='') Chọn ngôn ngữ
                  option(value='Tiếng Việt') Tiếng Việt
                  option(value='Tiếng Anh') Tiếng Anh
                  option(value='Tiếng Nhật') Tiếng Nhật
                  option(value='Tiếng Hàn') Tiếng Hàn
                  option(value='Tiếng Trung') Tiếng Trung
                  option(value='Khác') Khác
              .form-group
                label(for='theloai') Thể loại:
                select#theloai(name='category' required)
                  option(value='') Chọn thể loại
                  if categories && categories.length > 0
                    each category in categories
                      option(value=category._id)= category.name
              .form-group
                label(for='nhomsach') Nhóm sách:
                  span.optional (Tùy chọn)
                select#nhomsach(name='group')
                  option(value='') Không chọn nhóm sách
                  option(value='banchay') Bán chạy
                  option(value='sachmoi') Sách mới
                  option(value='manga') Manga
                  option(value='combo') Combo sách
                  option(value='tamlyhoc') Tâm lý học
                  option(value='selfhelp') Self-help
            .form-column
              .form-group
                label(for='dinhang') Định dạng:
                select#dinhang(name='format')
                  option(value='') Chọn định dạng
                  option(value='Bìa mềm') Bìa mềm
                  option(value='Bìa cứng') Bìa cứng
              .form-group
                label(for='sotrang') Số trang:
                input#sotrang(type='number' name='pageCount' min='1' placeholder='Số trang')
              .form-group
                label(for='giasach') Giá sách:
                input#giasach(type='number' name='price' placeholder='Nhập giá sách' required)
              .form-group
                label(for='soluong') Số lượng tồn kho:
                input#soluong(type='number' name='stock' min='0' max='10000' placeholder='Số lượng' required)
              .form-group
                label(for='daban') Đã bán:
                input#daban(type='number' name='sold' min='0' value='0' placeholder='Số lượng đã bán' readonly style='background-color: #f5f5f5; cursor: not-allowed;')
                small(style='color: #666; font-style: italic;') Số lượng đã bán sẽ được cập nhật tự động khi có đơn hàng
              .form-group
                label(for='anh') Ảnh:
                input#anh(type='file' name='image' required)
              .form-group.full-width
                label(for='mota') Mô tả:
                textarea#mota(name='description' rows='6' placeholder='Nhập mô tả sách...')
          .button-group
            button.btn-cancel(type='button' onclick="window.location.href='/admin/book'") Hủy
            button.btn-save(type='submit') Lưu
    script(src='/public/js/admin.js')
