doctype html
html(lang="vi")
    head
        title <PERSON><PERSON> toán
        meta(charset="UTF-8")
        meta(name="viewport", content="width=device-width, initial-scale=1.0")
        link(rel="stylesheet", href="/public/css/main.css")
        link(rel="stylesheet", href="/public/css/style.css")
        link(rel="stylesheet", href="/public/css/responsive.css")
        script(src="/public/js/addrForm.js")
        link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css")
    body
        include ../header.pug
        div#checkout-header
            //-Quay lại trang cart
            a(href="/cart")
                button.fa-solid.fa-arrow-left
            p Thanh toán
        div#checkout
            div.container
                div.info
                    div.info-head
                        i.fa-solid.fa-location-dot
                        p Địa chỉ giao hàng
                    div
                        div.info-user
                            div
                                p= user && user.address ? user.address.fullname : '<PERSON><PERSON><PERSON><PERSON>'
                                p= user && user.address ? user.address.phone : '0364105402'
                            p= user && user.address ? `${user.address.address}, ${user.address.ward}, ${user.address.district}, ${user.address.city}` : 'Thôn Kiêu Kỵ, Gia Lâm, Hà Nội, Xã Kiêu Kỵ, Huyện Gia Lâm, Hà Nội'
                            button.info-change Thay đổi
                    //-Nút chuyển sang change-address
                    button.fa-solid.fa-chevron-right
                div.prd-checkout
                    div.prd-left
                        h2 Sản phẩm
                    div.prd-right
                        p Đơn giá
                        p Số lượng
                        p Thành tiền

                div.checkout-products
                    each item, index in cart
                        div.prd(data-id=item.book._id)
                            div
                                img(src=item.book.image, alt=item.book.title)
                                div.prd-info
                                    h2= item.book.title
                                    div
                                        p #{item.price.toLocaleString('vi-VN')} đ
                                        p x#{item.quantity}
                                    div.prd-sum
                                        p Tổng tiền
                                        p #{(item.price * item.quantity).toLocaleString('vi-VN')} đ
                            div
                                p #{item.price.toLocaleString('vi-VN')} đ
                                p #{item.quantity}
                                p #{(item.price * item.quantity).toLocaleString('vi-VN')} đ

                form(action="/orders/create", method="POST", id="checkout-form")
                    // Hidden inputs cho sản phẩm
                    each item, index in cart
                        input(type="hidden", name=`items[${index}][book]`, value=item.book._id)
                        input(type="hidden", name=`items[${index}][quantity]`, value=item.quantity)
                        input(type="hidden", name=`items[${index}][price]`, value=item.price)

                    div.payment-method
                        h2 Phương thức thanh toán
                        input(type="checkbox", id="payment", name="paymentMethod", value="cod", class="cod-payment", checked)
                        label(for="payment")
                            i.fa-solid.fa-money-bill-wave
                            |  Thanh toán khi nhận hàng

                    div.note-checkout
                        h2 Thông tin khác
                        label(for="note") Ghi chú (tùy chọn)
                        textarea(type="text", name="note", class="form-note")
                    div.pay
                        div.pay-med
                            h2 Phương thức thanh toán
                            p Thanh toán khi nhận hàng
                        div
                            div.paym
                                p Tổng tiền
                                p#final-total #{total.toLocaleString('vi-VN')} đ
                            p Đã bao gồm thuế
                            button(type="submit", id="place-order-btn") Đặt hàng

                    // Hidden input for total price
                    input(type="hidden", name="totalPrice", id="total-input", value=total)

        div.change-addr
            div
                form(action="/customer/address/update", method="POST")
                    div.form-info
                        p Họ và tên
                        input(type="text", name="name", placeholder="Họ và tên", value=user && user.address ? user.address.fullname : "", required)
                    div.form-info
                        p Số điện thoại
                        input(type="text", name="phone", placeholder="Số điện thoại", value=user && user.address ? user.address.phone : "", required)
                    div.form-info
                        label(for="province") Tỉnh/Thành phố
                        select#province(name="city", required)
                            option(value="") Chọn Tỉnh/Thành phố
                            option(value="Hà Nội", selected=user && user.address && user.address.city === "Hà Nội") Hà Nội
                            option(value="TP. Hồ Chí Minh", selected=user && user.address && user.address.city === "TP. Hồ Chí Minh") TP. Hồ Chí Minh
                            option(value="Đà Nẵng", selected=user && user.address && user.address.city === "Đà Nẵng") Đà Nẵng
                    div.form-info
                        label(for="district") Quận/Huyện
                        select#district(name="district", required)
                            option(value="") Chọn Quận/Huyện
                            if user && user.address && user.address.district
                                option(value=user.address.district, selected)= user.address.district
                    div.form-info
                        label(for="ward") Xã/Phường
                        select#ward(name="ward", required)
                            option(value="") Chọn Xã/Phường
                            if user && user.address && user.address.ward
                                option(value=user.address.ward, selected)= user.address.ward
                    div.form-info
                        p Địa chỉ
                        input(type="text", name="address", placeholder="Địa chỉ", value=user && user.address ? user.address.address : "", required)
                    div.form-info
                        p Loại địa chỉ
                        div.radio-group
                            div
                                input(type="radio", name="type", value="home", checked=!user || !user.address || user.address.type !== "company")
                                label  Nhà riêng / Chung cư
                            div
                                input(type="radio", name="type", value="company", checked=user && user.address && user.address.type === "company")
                                label  Cơ quan / Công ty
                    div.form-info
                        div.btn-group
                            button.cancel(type="button") Hủy
                            button.create-update(type="submit") Cập nhật

        include ../footer.pug
    script.
        // Dữ liệu sản phẩm từ server
        const cartData = !{JSON.stringify(cart)};
        console.log('Dữ liệu cart từ server:', cartData);

        const changeBtn = document.querySelector(".info-change")
        const changeAdd = document.querySelector(".change-addr")
        const cancelBtn = document.querySelector(".cancel")
        const updateBtn = document.querySelector(".create-update")
        const totalAmountSpan = document.querySelector("#total-amount")
        const finalTotalP = document.querySelector("#final-total")
        const totalInput = document.querySelector("#total-input")
        const checkoutForm = document.querySelector("#checkout-form")

        // Tính tổng tiền cho tất cả sản phẩm
        function calculateTotal() {
            let total = 0;
            cartData.forEach(item => {
                total += item.price * item.quantity;
            });

            const formattedTotal = total.toLocaleString('vi-VN');
            totalAmountSpan.textContent = formattedTotal + ' đ';
            finalTotalP.textContent = formattedTotal + ' đ';
            totalInput.value = total;
        }

        // Khởi tạo
        calculateTotal();

        //mở form Change-addr
        changeBtn.addEventListener("click", function(){
            changeAdd.classList.add("open")
        })

        //Theo dõi màn hình khi chiều rộng nhỏ hơn quy định sẽ tự động đóng form Change-addr
        window.addEventListener("resize", function () {
            if (window.innerWidth < 631) {
                changeAdd.classList.remove("open");
            }
        })
        //nút hủy
        cancelBtn.addEventListener("click", function(){
            changeAdd.classList.remove("open")
        })
        //Nút cập nhật
        updateBtn.addEventListener("click", function(){
            changeAdd.classList.remove("open")
        })

    script(src='/public/js/search.js')