const express = require('express');
const router = express.Router();
const reviewController = require('../controllers/reviewController');
const { auth } = require('../middleware/authMiddleware');

router.post('/add', auth, reviewController.addReviewFromOrder);
router.post('/:bookId', auth, reviewController.addReview);
router.get('/:bookId', reviewController.getReviewsByBook);
router.get('/user/:bookId', auth, reviewController.getUserReviewForBook);

module.exports = router;