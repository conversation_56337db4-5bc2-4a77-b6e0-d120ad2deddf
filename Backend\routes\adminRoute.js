const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const { auth, isAdmin } = require('../middleware/authMiddleware');
const multer = require('multer');
const path = require('path');

// Cấu hình multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../../Frontend/public/uploads'));
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'book-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // Giới hạn 5MB
  fileFilter: function (req, file, cb) {
    // Chỉ chấp nhận file hình ảnh
    if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
      return cb(new Error('Chỉ chấp nhận file hình ảnh!'), false);
    }
    cb(null, true);
  }
});

// Middleware xác thực cho tất cả các route admin
router.use(auth, isAdmin);



// Dashboard - Trang chủ admin
router.get('/', adminController.getDashboardSummary);

// Quản lý người dùng
router.get('/user', adminController.getAllUsers);
router.get('/user/edit/:id', adminController.getUserForEdit);
router.post('/user/edit/:id', adminController.updateUser);
router.delete('/user/delete/:id', adminController.deleteUser);

// Quản lý sách
router.get('/book', adminController.getAllBooks);
router.get('/book/add', adminController.getAddBookForm);
router.post('/book/add', upload.single('image'), adminController.addBook);
router.get('/book/edit/:id', adminController.getBookForEdit);
router.post('/book/edit/:id', upload.single('image'), adminController.updateBook);
router.delete('/book/delete/:id', adminController.deleteBook);

// Quản lý danh mục
router.get('/category', adminController.getAllCategories);
router.get('/category/add', (req, res) => {
  res.render('admin/addcategory');
});
router.post('/category/add', adminController.addCategory);
router.get('/category/edit/:id', adminController.getCategoryForEdit);
router.post('/category/edit/:id', adminController.updateCategory);
router.delete('/category/delete/:id', adminController.deleteCategory);

// Quản lý đơn hàng
router.get('/order', adminController.getAllOrders);
router.get('/order/view/:id', adminController.getOrderDetails);
router.post('/order/update-status/:id', adminController.updateOrderStatus);
router.post('/order/cancel/:id', adminController.cancelOrder);

// Quản lý doanh thu
router.get('/sale', adminController.getSalesData);

// Xử lý lỗi multer
router.use((err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File quá lớn. Kích thước tối đa là 5MB'
      });
    }
    return res.status(400).json({
      success: false,
      message: `Lỗi upload file: ${err.message}`
    });
  }
  next(err);
});

module.exports = router;
