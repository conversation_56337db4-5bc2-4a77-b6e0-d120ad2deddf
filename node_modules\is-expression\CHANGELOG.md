# Change Log

Version 4.0.0 onwards are documented in [Releases](https://github.com/pugjs/is-expression/releases).

This project adheres to [Semantic Versioning](http://semver.org/).

## 3.0.0 - 2016-09-11
### Added
- Updated to acorn ~4.0.0
  - ES2016 has been made the default `ecmaVersion`.
  - Async functions are now implemented for `{ecmaVersion: 8}`.
  - See [acorn's CHANGELOG][acorn-4.0.0] for a full list of changes.

## 2.1.0 - 2016-07-27
### Added
- Updated to acorn ~3.3.0
  - The ES2016 check for strict mode in function parameters is now implemented
    for `{ecmaVersion: 7}`.
  - See [acorn's CHANGELOG][acorn-3.3.0] for a full list of changes.

## 2.0.1 - 2016-06-04
### Added
- Updated to acorn ~3.1.0
  - See [acorn's CHANGELOG][acorn-3.1.0] for a list of changes.
  - Even though it is a minor version bump for acorn, the new features are not
    in parts of acorn we are using, and thus a patch level bump is warranted.

## 2.0.0 - 2016-02-12
### Added
- Updated to acorn ~3.0.2
  - See [acorn's CHANGELOG][acorn-3.0.0] for a list of breaking changes.

## 1.0.2 - 2016-01-06
### Added
- Updated to acorn ~2.7.0

## 1.0.1 - 2015-11-12
### Fixed
- Use a stricter version range for Acorn since we depend on Acorn internals.

## 1.0.0 - 2015-11-11
### Added
- Initial release

[acorn-4.0.0]: https://github.com/ternjs/acorn/blob/master/CHANGELOG.md#400-2016-08-07
[acorn-3.3.0]: https://github.com/ternjs/acorn/blob/master/CHANGELOG.md#330-2016-07-25
[acorn-3.1.0]: https://github.com/ternjs/acorn/blob/master/CHANGELOG.md#310-2016-04-18
[acorn-3.0.0]: https://github.com/ternjs/acorn/blob/master/CHANGELOG.md#300-2016-02-10
