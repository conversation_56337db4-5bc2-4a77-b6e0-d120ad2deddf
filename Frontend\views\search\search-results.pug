doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title= `<PERSON><PERSON>t quả tìm kiếm: ${query} - BOOKSTORE`
    link(rel='stylesheet' href='/public/css/main.css')
    link(rel='stylesheet' href='/public/css/responsive.css')
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css')
  body
    include ../header.pug

    main#search-results
      div.container
        div.search-header
          h1 Kết quả tìm kiếm cho: "#{query}"
          p Tìm thấy #{total} sản phẩm

        if books && books.length > 0
          div.search-content
            div.products-grid
              each book in books
                div.product-item
                  a(href=`/books/${book._id}`)
                    div.product-image
                      img(src=book.image || '/public/img/default-book.jpg', alt=book.title)
                      if book.stock <= 0
                        div.out-of-stock-badge Hết hàng
                    div.product-info
                      h3.product-title= book.title
                      if book.author
                        p.product-author Tác giả: #{book.author}
                      div.product-price
                        span.price #{book.price.toLocaleString('vi-VN')} đ
                      div.product-stats
                        span.sold Đã bán: #{book.sold || 0}
                        if book.stock > 0
                          span.stock Còn: #{book.stock}
                        else
                          span.stock.out-of-stock Hết hàng

            // Pagination
            if totalPages > 1
              div.pagination
                if hasPrevPage
                  a.page-btn(href=`/search?q=${encodeURIComponent(query)}&page=${prevPage}`)
                    i.fa-solid.fa-chevron-left
                    span Trước

                - for (let i = 1; i <= totalPages; i++)
                  if i === currentPage
                    span.page-btn.active= i
                  else if i <= 3 || i > totalPages - 3 || Math.abs(i - currentPage) <= 2
                    a.page-btn(href=`/search?q=${encodeURIComponent(query)}&page=${i}`)= i
                  else if i === 4 && currentPage > 6
                    span.page-btn.dots ...
                  else if i === totalPages - 3 && currentPage < totalPages - 5
                    span.page-btn.dots ...

                if hasNextPage
                  a.page-btn(href=`/search?q=${encodeURIComponent(query)}&page=${nextPage}`)
                    span Sau
                    i.fa-solid.fa-chevron-right
        else
          div.no-results
            div.no-results-content
              i.fa-solid.fa-search
              h2 Không tìm thấy kết quả nào
              p Không có sản phẩm nào phù hợp với từ khóa "#{query}"
              a.back-home(href='/') Quay về trang chủ

    include ../footer.pug

    script(src='/public/js/search.js')

    style.
      #search-results {
        min-height: 60vh;
        padding: 20px 0;
      }

      .search-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
      }

      .search-header h1 {
        color: #333;
        margin-bottom: 10px;
        font-size: 28px;
      }

      .search-header p {
        color: #666;
        font-size: 16px;
      }

      .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
      }

      .product-item {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
      }

      .product-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
      }

      .product-image {
        position: relative;
        height: 200px;
        overflow: hidden;
      }

      .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .out-of-stock-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #dc3545;
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
      }

      .product-info {
        padding: 15px;
      }

      .product-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
        color: #333;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .product-author {
        font-size: 14px;
        color: #666;
        margin-bottom: 10px;
      }

      .product-price {
        margin-bottom: 10px;
      }

      .price {
        font-size: 18px;
        font-weight: bold;
        color: #c92127;
      }

      .product-stats {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #888;
      }

      .stock.out-of-stock {
        color: #dc3545;
        font-weight: bold;
      }

      .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-top: 30px;
      }

      .page-btn {
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
        text-decoration: none;
        color: #333;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 5px;
      }

      .page-btn:hover {
        background-color: #c92127;
        color: white;
        border-color: #c92127;
      }

      .page-btn.active {
        background-color: #c92127;
        color: white;
        border-color: #c92127;
      }

      .page-btn.dots {
        border: none;
        cursor: default;
      }

      .page-btn.dots:hover {
        background-color: transparent;
        color: #333;
      }

      .no-results {
        text-align: center;
        padding: 60px 20px;
      }

      .no-results-content i {
        font-size: 80px;
        color: #ddd;
        margin-bottom: 20px;
      }

      .no-results h2 {
        color: #333;
        margin-bottom: 15px;
      }

      .no-results p {
        color: #666;
        margin-bottom: 30px;
        font-size: 16px;
      }

      .suggestions {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin: 20px auto;
        max-width: 400px;
      }

      .suggestions h3 {
        color: #333;
        margin-bottom: 15px;
      }

      .suggestions ul {
        list-style: none;
        padding: 0;
      }

      .suggestions li {
        padding: 5px 0;
        color: #666;
      }

      .suggestions li:before {
        content: "• ";
        color: #c92127;
        font-weight: bold;
      }

      .back-home {
        display: inline-block;
        padding: 12px 30px;
        background-color: #c92127;
        color: white;
        text-decoration: none;
        border-radius: 25px;
        transition: background-color 0.3s ease;
        margin-top: 20px;
      }

      .back-home:hover {
        background-color: #a01e24;
      }

      @media (max-width: 768px) {
        .products-grid {
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 15px;
        }

        .search-header h1 {
          font-size: 24px;
        }

        .pagination {
          flex-wrap: wrap;
          gap: 5px;
        }

        .page-btn {
          padding: 8px 12px;
          font-size: 14px;
        }
      }
