doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title= `Kết quả tìm kiếm: ${query} - BOOKSTORE`
    link(rel='stylesheet' href='/public/css/main.css')
    link(rel='stylesheet' href='/public/css/responsive.css')
    link(rel="stylesheet", href="/public/css/search.css")
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css')
  body
    include ../header.pug

    main#search-results
      div.container
        div.search-header
          h1 Kết quả tìm kiếm cho: "#{query}"
          p Tìm thấy #{total} sản phẩm
        if books && books.length > 0
          div.search-content
            div.products-grid
              each book in books
                div.product-item
                  a(href=`/books/${book._id}`)
                    div.product-image
                      img(src=book.image || '/public/img/default-book.jpg', alt=book.title)
                      if book.stock <= 0
                        div.out-of-stock-badge Hết hàng
                    div.product-info
                      h3.product-title= book.title
                      div.product-price
                        span.price #{book.price.toLocaleString('vi-VN')} đ
                      div.product-stats
                        span.sold Đã bán: #{book.sold || 0}
                        if book.stock > 0
                          span.stock Còn: #{book.stock}
                        else
                          span.stock.out-of-stock Hết hàng
            if totalPages > 1
              div.pagination
                if hasPrevPage
                  a.page-btn(href=`/search?q=${encodeURIComponent(query)}&page=${prevPage}`)
                    i.fa-solid.fa-chevron-left
                    span Trước

                - for (let i = 1; i <= totalPages; i++)
                  if i === currentPage
                    span.page-btn.active= i
                  else if i <= 3 || i > totalPages - 3 || Math.abs(i - currentPage) <= 2
                    a.page-btn(href=`/search?q=${encodeURIComponent(query)}&page=${i}`)= i
                  else if i === 4 && currentPage > 6
                    span.page-btn.dots ...
                  else if i === totalPages - 3 && currentPage < totalPages - 5
                    span.page-btn.dots ...

                if hasNextPage
                  a.page-btn(href=`/search?q=${encodeURIComponent(query)}&page=${nextPage}`)
                    span Sau
                    i.fa-solid.fa-chevron-right
        else
          div.no-results
            div.no-results-content
              i.fa-solid.fa-search
              h2 Không tìm thấy kết quả nào
              p Không có sản phẩm nào phù hợp với từ khóa "#{query}"
              a.back-home(href='/') Quay về trang chủ
    include ../footer.pug
    script(src='/public/js/search.js')
      