doctype html
html(lang="vi")
  head
    title Giỏ hàng
    meta(charset="UTF-8")
    meta(name="viewport", content="width=device-width, initial-scale=1.0")
    link(rel="stylesheet", href="/public/css/main.css")
    link(rel="stylesheet", href="/public/css/responsive.css")
    link(rel="stylesheet", href="/public/css/style.css")
    link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css")
  body
    include ../header.pug

    div#cart-header
      div
        a(href="/")
          button.fa-solid.fa-arrow-left
        p Giỏ hàng
      div
        button.btn-edit Sửa

    div#cart
      div.container
        div.cart-content
          div.cart-left
            h1 Giỏ hàng (#{cartItems && cartItems.length > 0 ? cartItems.length : 0} sản phẩm)
            if cartItems && cartItems.length === 0
              div.empty-order
                i.fa-solid.fa-bag-shopping
                p Bạn chưa có sản phẩm nào trong giỏ hàng
                a(href="/")
                  button Mua sắm ngay
            else
              div.prd-cart
                div.prd-left
                  input(type="checkbox" id="all" class="all-check")
                  label(for="all") Tất cả sách
                div.prd-right
                  p Đơn giá
                  p Số lượng
                  p Thành tiền
                  p: button.fa-solid.fa-trash#delete-header
              each item, index in cartItems
                div.prd(data-id=item.bookId ? item.bookId._id : item.productId)
                  div.prd-left
                    input(type="checkbox", class="book-check", name="book", value=(item.bookId ? item.bookId._id : item.productId), id=`book-${index}`)
                    label(for=`book-${index}`)
                      div
                        img(src=item.bookId ? item.bookId.image : item.image, alt="Ảnh sách")
                      div.prd-info
                        h2= item.bookId ? item.bookId.title : item.title
                        div
                          p #{(item.bookId ? item.bookId.price : item.price).toLocaleString()} đ
                          div
                            button.fa-solid.fa-chevron-up.increase-btn
                            p= item.quantity
                            button.fa-solid.fa-chevron-down.decrease-btn
                  div.prd-right
                    p #{(item.bookId ? item.bookId.price : item.price).toLocaleString()} đ
                    p
                      button.fa-solid.fa-chevron-up.increase-btn
                      | #{item.quantity}
                      button.fa-solid.fa-chevron-down.decrease-btn
                    - const itemTotal = (item.bookId ? item.bookId.price : item.price) * item.quantity
                    p #{itemTotal.toLocaleString()} đ
                    p: button.fa-solid.fa-trash.remove-btn

          div.cart-right
            div.cart-checkout
              div
                input(type="checkbox" class="all-check")
                label(for="all") Chọn tất cả
                button#delete-selected Xóa
              div
                p Tổng cộng (<span id="selected-count">0</span> sản phẩm):
                  span#selected-total 0 đ
                button#checkout-btn Thanh toán

            div.cart-edit.hidden
              div
                input(type="checkbox" class="all-check")
                label(for="all") Chọn tất cả (#{cartItems ? cartItems.length : 0})
              div
                button#delete-selected-edit Xóa
    include ../footer.pug

    script.
      document.addEventListener("DOMContentLoaded", () => {
        // Tăng số lượng
        document.querySelectorAll(".fa-chevron-up").forEach(btn => {
          btn.addEventListener("click", async () => {
            const parent = btn.closest(".prd");
            const id = parent.querySelector("input.book-check").value;

            const response = await fetch(`/cart/increase/${id}`, { method: "POST" });
            if (response.status === 400) {
              const data = await response.json();
              alert(data.message || "Không thể tăng thêm số lượng.");
            } else {
              location.reload();
            }
          });
        });

        // Giảm số lượng
        document.querySelectorAll(".fa-chevron-down").forEach(btn => {
          btn.addEventListener("click", async () => {
            const parent = btn.closest(".prd");
            const id = parent.querySelector("input.book-check").value;

            const response = await fetch(`/cart/decrease/${id}`, { method: "POST" });
            if (response.status === 400) {
              const data = await response.json();
              alert(data.message || "Không thể giảm số lượng.");
            } else {
              location.reload();
            }
          });
        });

        // Xóa sản phẩm
        document.querySelectorAll(".fa-trash").forEach(btn => {
          btn.addEventListener("click", async () => {
            const parent = btn.closest(".prd");
            const id = parent.querySelector("input.book-check").value;

            await fetch(`/cart/remove/${id}`, { method: "POST" });
            location.reload();
          });
        });

        // Toggle chế độ sửa
        const editBtn = document.querySelector('.btn-edit');
        const cartCheckout = document.querySelector('.cart-checkout');
        const cartEdit = document.querySelector('.cart-edit');

        if (editBtn && cartCheckout && cartEdit) {
          editBtn.addEventListener('click', () => {
            const isEdit = editBtn.textContent.trim() === 'Sửa';
            editBtn.textContent = isEdit ? 'Hoàn thành' : 'Sửa';
            cartCheckout.classList.toggle('hidden', isEdit);
            cartEdit.classList.toggle('hidden', !isEdit);
          });
        }

        // Dữ liệu cart từ server
        const cartData = !{JSON.stringify(cartItems)};

        // Checkbox "Chọn tất cả"
        const allCheckboxes = document.querySelectorAll(".all-check");
        const bookCheckboxes = document.querySelectorAll(".book-check");
        const selectedCountSpan = document.querySelector("#selected-count");
        const selectedTotalSpan = document.querySelector("#selected-total");
        const checkoutBtn = document.querySelector("#checkout-btn");

        // Hàm tính tổng tiền sản phẩm được chọn
        function updateSelectedTotal() {
          const checkedBoxes = document.querySelectorAll(".book-check:checked");
          let total = 0;
          let count = 0;

          checkedBoxes.forEach(checkbox => {
            const bookId = checkbox.value;
            const item = cartData.find(item =>
              (item.bookId && item.bookId._id === bookId) ||
              (item.productId === bookId)
            );

            if (item) {
              const price = item.bookId ? item.bookId.price : item.price;
              total += price * item.quantity;
              count++;
            }
          });

          selectedCountSpan.textContent = count;
          selectedTotalSpan.textContent = total.toLocaleString('vi-VN') + ' đ';

          // Disable nút thanh toán nếu không có sản phẩm nào được chọn
          checkoutBtn.disabled = count === 0;
          if (count === 0) {
            checkoutBtn.style.opacity = '0.5';
            checkoutBtn.style.cursor = 'not-allowed';
          } else {
            checkoutBtn.style.opacity = '1';
            checkoutBtn.style.cursor = 'pointer';
          }
        }

        allCheckboxes.forEach(allCb => {
          allCb.addEventListener("change", () => {
            bookCheckboxes.forEach(cb => cb.checked = allCb.checked);
            updateSelectedTotal();
          });
        });

        // Xử lý checkbox từng sản phẩm
        bookCheckboxes.forEach(checkbox => {
          checkbox.addEventListener("change", function() {
            const checkedCount = document.querySelectorAll(".book-check:checked").length;
            allCheckboxes.forEach(allCb => {
              allCb.checked = checkedCount === bookCheckboxes.length;
            });
            updateSelectedTotal();
          });
        });

        // Xử lý nút thanh toán
        checkoutBtn.addEventListener("click", function(e) {
          e.preventDefault();

          const checkedBoxes = document.querySelectorAll(".book-check:checked");
          if (checkedBoxes.length === 0) {
            alert("Vui lòng chọn ít nhất một sản phẩm để thanh toán!");
            return;
          }

          // Tạo form để gửi danh sách sản phẩm được chọn
          const form = document.createElement('form');
          form.method = 'POST';
          form.action = '/checkout';

          checkedBoxes.forEach((checkbox, index) => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = `selectedBooks[${index}]`;
            input.value = checkbox.value;
            form.appendChild(input);
          });

          document.body.appendChild(form);
          form.submit();
        });

        // Xử lý nút xóa sản phẩm được chọn
        const deleteSelectedBtn = document.querySelector("#delete-selected");
        const deleteSelectedEditBtn = document.querySelector("#delete-selected-edit");
        const deleteHeaderBtn = document.querySelector("#delete-header");

        console.log('Tìm thấy nút xóa chính:', !!deleteSelectedBtn);
        console.log('Tìm thấy nút xóa edit:', !!deleteSelectedEditBtn);
        console.log('Tìm thấy nút xóa header:', !!deleteHeaderBtn);

        function handleDeleteSelected() {
          const checkedBoxes = document.querySelectorAll(".book-check:checked");
          console.log('Số sản phẩm được chọn để xóa:', checkedBoxes.length);

          if (checkedBoxes.length === 0) {
            alert("Vui lòng chọn ít nhất một sản phẩm để xóa!");
            return;
          }

          // Log các ID sản phẩm sẽ xóa
          const bookIds = Array.from(checkedBoxes).map(cb => cb.value);
          console.log('Danh sách ID sản phẩm sẽ xóa:', bookIds);

          if (confirm(`Bạn có chắc chắn muốn xóa ${checkedBoxes.length} sản phẩm đã chọn?`)) {
            // Tạo array chứa các promise xóa
            const deletePromises = [];

            checkedBoxes.forEach(checkbox => {
              const bookId = checkbox.value;
              console.log('Đang xóa sản phẩm ID:', bookId);
              deletePromises.push(
                fetch(`/cart/remove/${bookId}`, { method: "POST" })
                  .then(response => {
                    console.log(`Xóa sản phẩm ${bookId}:`, response.status === 200 ? 'Thành công' : 'Thất bại');
                    return response;
                  })
              );
            });

            // Thực hiện tất cả các request xóa
            Promise.all(deletePromises)
              .then((responses) => {
                console.log('Tất cả request xóa đã hoàn thành:', responses.length);
                location.reload();
              })
              .catch(error => {
                console.error('Lỗi khi xóa sản phẩm:', error);
                alert('Có lỗi xảy ra khi xóa sản phẩm!');
              });
          }
        }

        if (deleteSelectedBtn) {
          deleteSelectedBtn.addEventListener("click", handleDeleteSelected);
        }

        if (deleteSelectedEditBtn) {
          deleteSelectedEditBtn.addEventListener("click", handleDeleteSelected);
        }

        if (deleteHeaderBtn) {
          deleteHeaderBtn.addEventListener("click", handleDeleteSelected);
        }

        // Khởi tạo tính tổng ban đầu
        updateSelectedTotal();
      });

    script(src='/public/js/search.js')
