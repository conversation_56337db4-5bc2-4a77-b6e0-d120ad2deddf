{"name": "assert-never", "version": "1.4.0", "description": "Helper function for exhaustive checks of discriminated unions in TypeScript", "main": "index.js", "typings": "index.d.ts", "files": ["index.js", "index.ts", "index.d.ts"], "scripts": {"build": "tsc", "prepublish": "npm run build && npm test", "test": "jest"}, "keywords": ["typescript", "discriminated unions", "assert", "never"], "repository": "aikoven/assert-never", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@babel/core": "^7.24.7", "@babel/preset-env": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@types/jest": "^29.5.12", "babel-jest": "^29.7.0", "jest": "^29.7.0", "type-assertions": "^1.1.0", "typescript": "^5.5.3"}}