# LUỒNG HOẠT ĐỘNG HỆ THỐNG ĐÁNH GIÁ SẢN PHẨM

## 📋 TỔNG QUAN

Hệ thống đánh giá cho phép khách hàng đánh giá **từng sản phẩm riêng biệt** trong đơn hàng đã mua thành công. Mỗi sản phẩm trong mỗi đơn hàng chỉ được đánh giá **1 lần duy nhất**.

---

## 🏗️ CẤU TRÚC DATABASE

### **Review Model** (`Backend/models/reviewModel.js`)
```javascript
{
  user: ObjectId,     // Người đánh giá
  book: ObjectId,     // Sách được đánh giá
  order: ObjectId,    // Đơn hàng chứa sách này
  rating: Number,     // Điểm đánh giá (1-5 sao)
  comment: String,    // Nội dung đánh giá
  createdAt: Date     // Thời gian đ<PERSON> gi<PERSON>
}
```

### **Ý nghĩa các trường:**
- **user + book + order**: <PERSON><PERSON><PERSON> bảo 1 sản phẩm trong 1 đơn hàng chỉ được đánh giá 1 lần
- **order**: Liên kết review với đơn hàng cụ thể (cho phép mua lại và đánh giá lại)

---

## 🔄 LUỒNG HOẠT ĐỘNG CHI TIẾT

### **BƯỚC 1: HIỂN THỊ DANH SÁCH ĐỚN HÀNG**

#### **File:** `Backend/controllers/orderController.js` - `getMyOrders()`

```javascript
// 1. Lấy danh sách đơn hàng của user
const orders = await Order.find({ user: userId })
  .populate('items.book', 'title image price')  // Lấy thông tin sách
  .sort({ createdAt: -1 })

// 2. Với mỗi đơn hàng đã giao thành công
for (let order of orders) {
  if (order.status === 'delivered') {

    // 3. Lấy tất cả reviews của user cho đơn hàng này
    const orderReviews = await Review.find({
      user: userId,
      order: order._id
    })

    // 4. Tạo map để biết sản phẩm nào đã được đánh giá
    order.itemReviews = []
    for (let review of orderReviews) {
      const reviewedItem = order.items.find(item =>
        item.book._id.toString() === review.book.toString()
      )
      if (reviewedItem) {
        order.itemReviews.push({
          itemId: reviewedItem._id,    // ID của item trong order
          reviewId: review._id,        // ID của review
          bookId: review.book          // ID của sách
        })
      }
    }
  }
}
```

#### **Kết quả:**
- Mỗi `order` có thêm field `itemReviews[]`
- Biết được sản phẩm nào đã có review, sản phẩm nào chưa

---

### **BƯỚC 2: HIỂN THỊ GIAO DIỆN**

#### **File:** `Frontend/views/customer/order/order.pug`

```pug
each order in orders
  // Hiển thị thông tin đơn hàng

  // Với mỗi sản phẩm trong đơn hàng
  each item in order.items
    div.prd-item
      // Hiển thị thông tin sản phẩm (ảnh, tên, giá)

      // Nếu đơn hàng đã giao thành công
      if order.status === 'delivered'

        // Kiểm tra sản phẩm này đã được đánh giá chưa
        - const itemReview = order.itemReviews.find(r =>
            r.itemId.toString() === item._id.toString())

        if itemReview
          // Đã có đánh giá → hiển thị nút "Xem đánh giá"
          button.btn-view-review(data-review-id=itemReview.reviewId)
            | Xem đánh giá
        else
          // Chưa có đánh giá → hiển thị nút "Đánh giá"
          button.btn-review(
            data-order-item-id=item._id,
            data-book-title=item.book.title
          ) Đánh giá
```

#### **Kết quả UI:**
```
📦 Đơn hàng #OD001 - Đã giao thành công
├── 📖 Sách A - [Đánh giá]     ← Chưa đánh giá
├── 📖 Sách B - [Xem đánh giá] ← Đã đánh giá
└── 📖 Sách C - [Đánh giá]     ← Chưa đánh giá
```

---

### **BƯỚC 3: NGƯỜI DÙNG CLICK "ĐÁNH GIÁ"**

#### **File:** `Frontend/views/customer/order/order.pug` - JavaScript

```javascript
// Lắng nghe click vào nút "Đánh giá"
reviewBtn.forEach(btn => {
  btn.addEventListener("click", function () {

    // Lấy thông tin từ button attributes
    const orderItemId = this.getAttribute('data-order-item-id')  // ID của item
    const bookTitle = this.getAttribute('data-book-title')       // Tên sách

    // Set vào form ẩn
    document.getElementById('orderItemId').value = orderItemId

    // Cập nhật tiêu đề modal
    document.querySelector('.review h3').textContent = `Đánh giá: ${bookTitle}`

    // Hiển thị modal đánh giá
    review.classList.add("open")
  })
})
```

#### **Kết quả:**
- Modal đánh giá hiển thị với tên sách cụ thể
- Form có `orderItemId` để biết đang đánh giá sản phẩm nào

---

### **BƯỚC 4: NGƯỜI DÙNG GỬI ĐÁNH GIÁ**

#### **File:** `Frontend/views/customer/order/order.pug` - Form

```pug
form(action="/reviews/add", method="POST")
  h3 Đánh giá sản phẩm
  input(type="hidden", name="orderItemId", id="orderItemId")  // ID của item

  // Rating stars (1-5 sao)
  input.star(type="radio", name="rating", value="5")
  input.star(type="radio", name="rating", value="4")
  // ...

  // Nội dung đánh giá
  textarea(name="content", required, placeholder="Nhập nhận xét")

  button(type="submit") Gửi đánh giá
```

#### **Data gửi lên server:**
```javascript
{
  orderItemId: "60f7b3b3b3b3b3b3b3b3b3b3",  // ID của item trong order
  rating: 5,                                // Số sao
  content: "Sách rất hay, nội dung bổ ích"  // Nhận xét
}
```

---

### **BƯỚC 5: XỬ LÝ ĐÁNH GIÁ Ở SERVER**

#### **File:** `Backend/controllers/reviewController.js` - `addReviewFromOrder()`

```javascript
const addReviewFromOrder = async (req, res) => {
  try {
    const { orderItemId, rating, content } = req.body
    const userId = req.user._id

    // 1. Tìm đơn hàng chứa orderItemId
    const order = await Order.findOne({
      user: userId,
      'items._id': orderItemId,    // Tìm order có item với ID này
      status: 'delivered'          // Chỉ cho phép đánh giá đơn hàng đã giao
    })

    if (!order) {
      return res.redirect('/customer/orders?error=Không tìm thấy đơn hàng')
    }

    // 2. Tìm item cụ thể trong order
    const orderItem = order.items.find(item =>
      item._id.toString() === orderItemId
    )

    if (!orderItem) {
      return res.redirect('/customer/orders?error=Không tìm thấy sản phẩm')
    }

    const bookId = orderItem.book

    // 3. Kiểm tra đã đánh giá sản phẩm này trong đơn hàng này chưa
    const existingReview = await Review.findOne({
      user: userId,
      order: order._id,
      book: bookId
    })

    if (existingReview) {
      return res.redirect('/customer/orders?error=Bạn đã đánh giá sản phẩm này rồi')
    }

    // 4. Tạo đánh giá mới
    const review = new Review({
      user: userId,
      book: bookId,
      order: order._id,
      rating: parseInt(rating),
      comment: content
    })

    await review.save()

    // 5. Redirect về trang đơn hàng với thông báo thành công
    res.redirect('/customer/orders?success=Đánh giá thành công')

  } catch (error) {
    console.error('Lỗi khi thêm đánh giá:', error)
    res.redirect('/customer/orders?error=Có lỗi xảy ra')
  }
}
```

#### **Các bước validation:**
1. ✅ **Kiểm tra đơn hàng**: Có tồn tại và thuộc về user không?
2. ✅ **Kiểm tra trạng thái**: Đơn hàng đã giao thành công chưa?
3. ✅ **Kiểm tra sản phẩm**: Item có tồn tại trong order không?
4. ✅ **Kiểm tra trùng lặp**: Đã đánh giá sản phẩm này trong đơn hàng này chưa?
5. ✅ **Lưu đánh giá**: Tạo record mới trong database

---

### **BƯỚC 6: CẬP NHẬT GIAO DIỆN SAU KHI ĐÁNH GIÁ**

Sau khi đánh giá thành công, user được redirect về `/customer/orders`.

Hệ thống sẽ:

1. **Reload danh sách đơn hàng** (quay lại BƯỚC 1)
2. **Phát hiện review mới** trong `order.itemReviews`
3. **Cập nhật UI**: Nút "Đánh giá" → "Xem đánh giá"

```
📦 Đơn hàng #OD001 - Đã giao thành công
├── 📖 Sách A - [Xem đánh giá] ← Đã đánh giá (mới)
├── 📖 Sách B - [Xem đánh giá] ← Đã đánh giá
└── 📖 Sách C - [Đánh giá]     ← Chưa đánh giá
```

---

### **BƯỚC 7: XEM CHI TIẾT ĐÁNH GIÁ**

#### **File:** `Backend/controllers/reviewController.js` - `getReviewById()`

```javascript
const getReviewById = async (req, res) => {
  try {
    const reviewId = req.params.reviewId

    // Lấy thông tin đánh giá với đầy đủ thông tin liên quan
    const review = await Review.findById(reviewId)
      .populate('user', 'username')                    // Thông tin người đánh giá
      .populate('book', 'title author image price')    // Thông tin sách
      .populate('order', 'createdAt totalPrice')       // Thông tin đơn hàng

    // Kiểm tra quyền xem (chỉ người tạo mới được xem)
    if (review.user._id.toString() !== req.user._id.toString()) {
      return res.redirect('/customer/orders?error=Không có quyền xem')
    }

    // Render trang xem đánh giá
    res.render('customer/review/view-review', {
      review: review,
      user: req.user
    })

  } catch (error) {
    res.redirect('/customer/orders?error=Lỗi khi xem đánh giá')
  }
}
```

#### **Trang xem đánh giá hiển thị:**
- 📖 **Thông tin sách**: Ảnh, tên, tác giả, giá
- ⭐ **Đánh giá**: Số sao và nhận xét
- 📅 **Thời gian**: Ngày đánh giá và ngày mua
- 🔙 **Nút quay lại** trang đơn hàng

---

## 🎯 CÁC TÌNH HUỐNG THỰC TẾ

### **Tình huống 1: Đơn hàng 1 sản phẩm**
```
📦 Đơn hàng #OD001
└── 📖 Sách A - [Đánh giá] → [Xem đánh giá]
```

### **Tình huống 2: Đơn hàng nhiều sản phẩm**
```
📦 Đơn hàng #OD002
├── 📖 Sách A - [Đánh giá]     ← Có thể đánh giá
├── 📖 Sách B - [Đánh giá]     ← Có thể đánh giá
└── 📖 Sách C - [Đánh giá]     ← Có thể đánh giá

// Sau khi đánh giá Sách A:
├── 📖 Sách A - [Xem đánh giá] ← Đã đánh giá
├── 📖 Sách B - [Đánh giá]     ← Vẫn có thể đánh giá
└── 📖 Sách C - [Đánh giá]     ← Vẫn có thể đánh giá
```

### **Tình huống 3: Mua lại sách đã từng mua**
```
📦 Đơn hàng #OD001 (cũ)
└── 📖 Sách A - [Xem đánh giá] ← Đã đánh giá lần 1

📦 Đơn hàng #OD003 (mới - mua lại)
└── 📖 Sách A - [Đánh giá]     ← Có thể đánh giá lần 2
```

---

## 🔒 BẢO MẬT VÀ VALIDATION

### **Các điều kiện để đánh giá:**
1. ✅ **Đã đăng nhập**: Có `req.user._id`
2. ✅ **Đã mua sản phẩm**: Có order chứa sản phẩm đó
3. ✅ **Đơn hàng đã giao**: `order.status === 'delivered'`
4. ✅ **Chưa đánh giá**: Không có review cho `user + order + book`

### **Các điều kiện để xem đánh giá:**
1. ✅ **Đã đăng nhập**: Có `req.user._id`
2. ✅ **Là chủ sở hữu**: `review.user._id === req.user._id`

---

## 📁 CẤU TRÚC FILE

```
Backend/
├── models/
│   ├── reviewModel.js          # Schema đánh giá
│   ├── orderModel.js           # Schema đơn hàng
│   └── bookModel.js            # Schema sách
├── controllers/
│   ├── reviewController.js     # Logic xử lý đánh giá
│   └── orderController.js      # Logic xử lý đơn hàng
└── routes/
    ├── reviewRoute.js          # Routes cho đánh giá
    └── userRoute.js            # Routes cho user

Frontend/
├── views/
│   ├── customer/order/
│   │   └── order.pug           # Trang danh sách đơn hàng
│   └── customer/review/
│       └── view-review.pug     # Trang xem chi tiết đánh giá
└── public/css/
    └── main.css                # Styles cho UI
```

---

## 🚀 KẾT LUẬN

Hệ thống đánh giá này cho phép:

✅ **Đánh giá từng sản phẩm riêng biệt** trong đơn hàng
✅ **Mua lại và đánh giá lại** sản phẩm đã từng mua
✅ **Quản lý chặt chẽ** quyền đánh giá và xem đánh giá
✅ **UI trực quan** và dễ sử dụng
✅ **Bảo mật tốt** với nhiều lớp validation

Luồng hoạt động rõ ràng, logic chặt chẽ, đảm bảo tính nhất quán của dữ liệu và trải nghiệm người dùng tốt.

---

## 🔧 CÁC ROUTES VÀ API ENDPOINTS

### **Routes chính:**

```javascript
// Backend/routes/reviewRoute.js
POST /reviews/add                    // Thêm đánh giá từ đơn hàng
GET  /reviews/:reviewId              // Xem chi tiết đánh giá
GET  /reviews/:bookId                // Lấy tất cả đánh giá của sách
POST /reviews/:bookId                // Đánh giá sách (cách cũ)

// Backend/routes/userRoute.js
GET  /customer/orders                // Danh sách đơn hàng
POST /customer/orders/:orderId/cancel // Hủy đơn hàng
```

### **Middleware sử dụng:**
- `auth`: Kiểm tra đăng nhập
- `loadUser`: Load thông tin user vào request

---

## 🎨 GIAO DIỆN VÀ CSS

### **CSS Classes quan trọng:**

```css
/* Nút đánh giá cho từng sản phẩm */
.item-review-btn {
  font-size: 12px !important;
  padding: 6px 12px !important;
  margin-top: 8px !important;
  border-radius: 4px !important;
}

/* Nút xem đánh giá */
.btn-view-review {
  background-color: #007bff;
  border: 2px solid #007bff;
  color: #fff;
}

/* Nút đánh giá */
.btn-review {
  background-color: #28a745;
  border: 2px solid #28a745;
  color: #fff;
}

/* Modal đánh giá */
.review {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: none;
}

.review.open {
  display: flex;
  align-items: center;
  justify-content: center;
}
```

### **JavaScript Events:**

```javascript
// Xử lý click nút đánh giá
document.querySelectorAll('.btn-review').forEach(btn => {
  btn.addEventListener('click', function() {
    // Lấy thông tin sản phẩm
    // Hiển thị modal
    // Set form data
  })
})

// Xử lý click nút xem đánh giá
document.querySelectorAll('.btn-view-review').forEach(btn => {
  btn.addEventListener('click', function() {
    // Redirect đến trang xem đánh giá
    window.location.href = `/reviews/${reviewId}`
  })
})

// Xử lý submit form đánh giá
document.querySelector('.review form').addEventListener('submit', function(e) {
  // Validation
  // Submit form
})
```

---

## 🐛 XỬ LÝ LỖI VÀ EDGE CASES

### **Các lỗi có thể xảy ra:**

#### **1. Lỗi dữ liệu:**
```javascript
// Sản phẩm không tồn tại (đã bị xóa)
if (!orderItem.book) {
  return res.redirect('/customer/orders?error=Sản phẩm không tồn tại')
}

// BookId không hợp lệ
if (!bookId || bookId === 'null') {
  return res.redirect('/customer/orders?error=Sản phẩm không hợp lệ')
}
```

#### **2. Lỗi quyền truy cập:**
```javascript
// Không phải chủ sở hữu đơn hàng
if (order.user.toString() !== userId.toString()) {
  return res.status(403).json({ error: 'Không có quyền truy cập' })
}

// Đơn hàng chưa giao
if (order.status !== 'delivered') {
  return res.redirect('/customer/orders?error=Chỉ có thể đánh giá đơn hàng đã giao')
}
```

#### **3. Lỗi trùng lặp:**
```javascript
// Đã đánh giá sản phẩm này rồi
const existingReview = await Review.findOne({
  user: userId,
  order: orderId,
  book: bookId
})

if (existingReview) {
  return res.redirect('/customer/orders?error=Đã đánh giá sản phẩm này rồi')
}
```

### **Error Handling Strategy:**

1. **Validation ở Frontend**: Kiểm tra form trước khi submit
2. **Validation ở Backend**: Kiểm tra dữ liệu và quyền truy cập
3. **Try-Catch**: Bắt lỗi runtime và database
4. **User-friendly Messages**: Thông báo lỗi dễ hiểu
5. **Logging**: Ghi log lỗi để debug

---

## 📊 PERFORMANCE VÀ TỐI ƯU

### **Database Optimization:**

```javascript
// Sử dụng lean() để tăng tốc query
const orders = await Order.find(filter)
  .populate('items.book', 'title image price')
  .lean()  // Trả về plain object thay vì Mongoose document

// Index cho performance
// Trong reviewModel.js
reviewSchema.index({ user: 1, order: 1, book: 1 })  // Composite index
reviewSchema.index({ book: 1, createdAt: -1 })      // Cho việc hiển thị reviews
```

### **Frontend Optimization:**

```javascript
// Debounce cho search
let searchTimeout
function debounceSearch(query) {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    fetchSuggestions(query)
  }, 300)
}

// Lazy loading cho images
<img src="placeholder.jpg" data-src="real-image.jpg" class="lazy-load">

// Event delegation thay vì multiple listeners
document.addEventListener('click', function(e) {
  if (e.target.classList.contains('btn-review')) {
    // Handle review button click
  }
  if (e.target.classList.contains('btn-view-review')) {
    // Handle view review button click
  }
})
```

### **Caching Strategy:**

```javascript
// Cache categories (ít thay đổi)
const categories = await Category.find().sort({ name: 1 }).cache(3600) // 1 hour

// Cache user profile
const user = await User.findById(userId).select('-password').cache(1800) // 30 min
```

---

## 🧪 TESTING VÀ DEBUGGING

### **Unit Tests:**

```javascript
// Test review creation
describe('Review Creation', () => {
  it('should create review for valid order item', async () => {
    const reviewData = {
      orderItemId: validOrderItemId,
      rating: 5,
      content: 'Great book!'
    }

    const response = await request(app)
      .post('/reviews/add')
      .send(reviewData)
      .expect(302) // Redirect

    const review = await Review.findOne({
      user: userId,
      order: orderId,
      book: bookId
    })

    expect(review).toBeTruthy()
    expect(review.rating).toBe(5)
  })

  it('should prevent duplicate reviews', async () => {
    // Create first review
    await createReview(reviewData)

    // Try to create duplicate
    const response = await request(app)
      .post('/reviews/add')
      .send(reviewData)
      .expect(302)

    expect(response.headers.location).toContain('error=')
  })
})
```

### **Debug Tools:**

```javascript
// Logging middleware
app.use((req, res, next) => {
  console.log(`${req.method} ${req.path}`, {
    body: req.body,
    user: req.user?.username,
    timestamp: new Date().toISOString()
  })
  next()
})

// Debug helper
function debugOrder(order) {
  console.log('Order Debug:', {
    id: order._id,
    status: order.status,
    itemCount: order.items.length,
    itemReviews: order.itemReviews?.length || 0,
    items: order.items.map(item => ({
      id: item._id,
      book: item.book?.title || 'No book',
      hasReview: order.itemReviews?.some(r => r.itemId.toString() === item._id.toString())
    }))
  })
}
```

---

## 🔄 WORKFLOW DEVELOPMENT

### **Quy trình phát triển:**

1. **Phân tích yêu cầu**: Hiểu rõ business logic
2. **Thiết kế database**: Schema và relationships
3. **API design**: Routes và data flow
4. **Backend implementation**: Controllers và validation
5. **Frontend implementation**: UI và user interaction
6. **Testing**: Unit tests và integration tests
7. **Optimization**: Performance và user experience
8. **Documentation**: Code comments và user guide

### **Git Workflow:**

```bash
# Feature branch
git checkout -b feature/review-system

# Development commits
git add .
git commit -m "feat: add review model and schema"
git commit -m "feat: implement review creation logic"
git commit -m "feat: add review UI components"
git commit -m "fix: handle edge case for deleted products"

# Merge to main
git checkout main
git merge feature/review-system
```

---

## 📚 TÀI LIỆU THAM KHẢO

### **Technologies Used:**
- **Backend**: Node.js, Express.js, MongoDB, Mongoose
- **Frontend**: Pug templates, Vanilla JavaScript, CSS3
- **Authentication**: Session-based auth with middleware
- **File Upload**: Multer for image handling
- **Validation**: Custom validation functions

### **Best Practices Applied:**
- ✅ **MVC Pattern**: Separation of concerns
- ✅ **RESTful APIs**: Consistent endpoint design
- ✅ **Error Handling**: Comprehensive error management
- ✅ **Security**: Input validation và authorization
- ✅ **Performance**: Database optimization và caching
- ✅ **Maintainability**: Clean code và documentation

### **Future Enhancements:**
- 📧 **Email notifications** khi có đánh giá mới
- 📊 **Analytics dashboard** cho admin
- 🔍 **Advanced search** và filtering
- 📱 **Mobile app** với React Native
- 🌐 **Multi-language** support
- ⭐ **Review moderation** system

---

## 🎯 TỔNG KẾT

Hệ thống đánh giá này được thiết kế với:

🏗️ **Kiến trúc vững chắc**: MVC pattern, clear separation
🔒 **Bảo mật tốt**: Multiple validation layers
🚀 **Performance cao**: Optimized queries và caching
🎨 **UX/UI tốt**: Intuitive và responsive design
🧪 **Testable**: Unit tests và integration tests
📖 **Maintainable**: Clean code và comprehensive docs

Đây là một hệ thống hoàn chỉnh, production-ready với khả năng mở rộng cao và dễ bảo trì.
