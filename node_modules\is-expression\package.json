{"name": "is-expression", "version": "4.0.0", "files": ["index.js"], "description": "Check if a string is a valid JavaScript expression", "keywords": ["javascript", "expression"], "dependencies": {"acorn": "^7.1.1", "object-assign": "^4.1.1"}, "devDependencies": {"nyc": "^15.0.1", "testit": "^3.1.0"}, "scripts": {"test": "node test && npm run coverage", "coverage": "nyc node test.js"}, "repository": {"type": "git", "url": "https://github.com/pugjs/is-expression.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT"}