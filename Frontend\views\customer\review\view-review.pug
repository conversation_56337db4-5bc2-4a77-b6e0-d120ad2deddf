doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title Chi tiết đánh giá - BOOKSTORE
    link(rel='stylesheet' href='/public/css/main.css')
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css')
  body
    include ../../header.pug
    
    main#customer
      .container
        .customer-header
          h2 Chi tiết đánh giá
          a(href='/customer/orders') ← Quay lại đơn hàng
        
        .review-detail
          .review-book-info
            .book-image
              img(src=review.book.image || '/public/img/default-book.jpg' alt=review.book.title)
            .book-details
              h3= review.book.title
              p Tác giả: #{review.book.author}
              p Giá: #{review.book.price.toLocaleString('vi-VN')} đ
          
          .review-content
            .review-header
              h4 Đ<PERSON>h giá của bạn
              .review-date
                p <PERSON><PERSON>y đ<PERSON>h giá: #{new Date(review.createdAt).toLocaleDateString('vi-VN')}
                p Đơn hàng: #{new Date(review.order.createdAt).toLocaleDateString('vi-VN')}
            
            .review-rating
              p Đánh giá: 
              .stars
                - for (let i = 1; i <= 5; i++)
                  if i <= review.rating
                    i.fa-solid.fa-star.filled
                  else
                    i.fa-regular.fa-star
              span (#{review.rating}/5 sao)
            
            .review-comment
              h5 Nhận xét:
              p= review.comment
          
          .review-actions
            a(href='/customer/orders' class='btn btn-primary') Quay lại đơn hàng
    
    style.
      #customer .review-detail {
        background: white;
        border-radius: 10px;
        padding: 30px;
        margin-top: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      }
      
      .review-book-info {
        display: flex;
        gap: 20px;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
      }
      
      .review-book-info .book-image {
        width: 120px;
        height: 150px;
        flex-shrink: 0;
      }
      
      .review-book-info .book-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
      }
      
      .review-book-info .book-details h3 {
        color: #333;
        margin-bottom: 10px;
        font-size: 20px;
      }
      
      .review-book-info .book-details p {
        color: #666;
        margin-bottom: 8px;
        font-size: 14px;
      }
      
      .review-content .review-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      
      .review-content .review-header h4 {
        color: #333;
        font-size: 18px;
      }
      
      .review-date p {
        font-size: 12px;
        color: #888;
        margin: 0;
      }
      
      .review-rating {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 20px;
      }
      
      .review-rating .stars {
        display: flex;
        gap: 2px;
      }
      
      .review-rating .stars i {
        font-size: 16px;
        color: #ffc107;
      }
      
      .review-rating .stars i.fa-regular {
        color: #ddd;
      }
      
      .review-comment h5 {
        color: #333;
        margin-bottom: 10px;
        font-size: 16px;
      }
      
      .review-comment p {
        color: #555;
        line-height: 1.6;
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #c92127;
      }
      
      .review-actions {
        margin-top: 30px;
        text-align: center;
      }
      
      .review-actions .btn {
        padding: 12px 30px;
        background-color: #c92127;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        transition: background-color 0.3s ease;
      }
      
      .review-actions .btn:hover {
        background-color: #a01e24;
      }
      
      .customer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      
      .customer-header h2 {
        color: #333;
        font-size: 24px;
      }
      
      .customer-header a {
        color: #c92127;
        text-decoration: none;
        font-size: 14px;
      }
      
      .customer-header a:hover {
        text-decoration: underline;
      }
