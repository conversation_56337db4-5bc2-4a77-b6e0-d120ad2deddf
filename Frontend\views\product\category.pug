doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title= `Danh mục ${category || 'Sách'} - WEB BÁN SÁCH TRUYỆN NHÓM 9`
    link(rel="stylesheet", href="/public/css/main.css")
    link(rel="stylesheet", href="/public/css/col.css")
    link(rel="stylesheet", href="/public/css/responsive.css")
    link(rel="stylesheet", href="/public/css/style.css")
    script(src='/public/js/logout.js')
    link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css")
  body
    include ../header.pug

    div(id="cart-header")
        //-Quay lại trang chủ
        div
            a(href="/" class="back-btn")
                button(class="fa-solid fa-arrow-left")
                span Quay lại trang chủ
        div
            h1 Danh mục

    div(id="category")
            div(class="container")
                div(class="category-left")
                    div
                        h2 Thể loại
                        ul
                            if categories && categories.length > 0
                                each categoryItem in categories
                                    li(class= category === categoryItem.name ? 'active' : '')
                                        a(href=`/books/category/${categoryItem._id}`)= categoryItem.name.charAt(0).toUpperCase() + categoryItem.name.slice(1).toLowerCase()
                            else
                                li: span Chưa có thể loại nào
                    div
                        h2 Khoảng giá
                        ul
                            li(class= req.query.maxPrice == '100000' ? 'active' : ''): a(href=`/books/category/${req.params.categoryName || 'all'}?maxPrice=100000`) Nhỏ hơn 100.000đ
                            li(class= req.query.minPrice == '100000' && req.query.maxPrice == '200000' ? 'active' : ''): a(href=`/books/category/${req.params.categoryName || 'all'}?minPrice=100000&maxPrice=200000`) Từ 100.000đ - 200.000đ
                            li(class= req.query.minPrice == '200000' && req.query.maxPrice == '300000' ? 'active' : ''): a(href=`/books/category/${req.params.categoryName || 'all'}?minPrice=200000&maxPrice=300000`) Từ 200.000đ - 300.000đ
                            li(class= req.query.minPrice == '300000' && req.query.maxPrice == '400000' ? 'active' : ''): a(href=`/books/category/${req.params.categoryName || 'all'}?minPrice=300000&maxPrice=400000`) Từ 300.000đ - 400.000đ
                            li(class= req.query.minPrice == '400000' && req.query.maxPrice == '500000' ? 'active' : ''): a(href=`/books/category/${req.params.categoryName || 'all'}?minPrice=400000&maxPrice=500000`) Từ 400.000đ - 500.000đ
                            li(class= req.query.minPrice == '500000' && !req.query.maxPrice ? 'active' : ''): a(href=`/books/category/${req.params.categoryName || 'all'}?minPrice=500000`) Lớn hơn 500.000đ
                div(class="category-right")
                    div(class="category-right-head")
                        h2= category || 'Tất cả sách'
                        div( class="sort-filter")
                    //-Sản phẩm hiển thị
                    div(class="books-grid")
                        if books && books.length > 0
                            each book in books
                                a(href=`/books/${book._id}` class="book-card"): div(class="book-item")
                                    div(class="book-image")
                                        img(src=book.image || '/public/img/default-book.jpg', alt=book.title)
                                    div(class="book-info")
                                        h4(class="book-title")= book.title
                                        div(class="book-price")
                                            span(class="price")= `${book.price.toLocaleString()} đ`
                                            span(class="sold") Đã bán: #{book.sold || 0}
                        else
                            div(class="no-products")
                                p Không có sản phẩm nào trong danh mục này
                    if pagination && pagination.pages > 1
                        div(class="listpage")
                            ul
                                if pagination.page > 1
                                    li: a(href=`/books/category/${category}?page=${pagination.page - 1}`): i(class="fa-solid fa-chevron-left")
                                - for (let i = 1; i <= pagination.pages; i++)
                                    li(class= pagination.page == i ? 'active' : ''): a(href=`/books/category/${category}?page=${i}`)= i
                                if pagination.page < pagination.pages
                                    li: a(href=`/books/category/${category}?page=${pagination.page + 1}`): i(class="fa-solid fa-chevron-right")

    include ../footer.pug
    script(src='/public/js/search.js')
