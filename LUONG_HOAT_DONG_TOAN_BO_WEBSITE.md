# LUỒNG HOẠT ĐỘNG TOÀN BỘ WEBSITE BOOKSTORE

## 📋 TỔNG QUAN HỆ THỐNG

Website Bookstore là một hệ thống bán sách trực tuyến hoàn chỉnh với 2 vai trò chính:
- 👤 **Customer (<PERSON><PERSON><PERSON><PERSON> hàng)**: <PERSON><PERSON><PERSON><PERSON>, <PERSON>ua s<PERSON>ch, quản lý đơn hàng, đ<PERSON><PERSON> giá
- 👨‍💼 **<PERSON><PERSON> (Quản trị viên)**: <PERSON><PERSON><PERSON><PERSON> l<PERSON>, đ<PERSON><PERSON> hà<PERSON>, ng<PERSON><PERSON><PERSON> dùng, doanh thu

---

## 🏗️ KIẾN TRÚC HỆ THỐNG

### **Technology Stack:**
```
Frontend: Pug Templates + Vanilla JavaScript + CSS3
Backend: Node.js + Express.js
Database: MongoDB + Mongoose
Authentication: Session-based
File Upload: Multer
```

### **C<PERSON>u trú<PERSON> thư mục:**
```
Bookstore-app/
├── Backend/
│   ├── controllers/     # Logic xử lý business
│   ├── models/         # Database schemas
│   ├── routes/         # API endpoints
│   ├── middleware/     # Authentication, validation
│   └── uploads/        # File uploads
├── Frontend/
│   ├── views/          # Pug templates
│   ├── public/         # Static files (CSS, JS, images)
│   └── uploads/        # Uploaded images
└── node_modules/       # Dependencies
```

---

## 🔐 HỆ THỐNG AUTHENTICATION

### **1. ĐĂNG KÝ TÀI KHOẢN**

#### **Luồng hoạt động:**
```
User nhập form → Validation → Hash password → Lưu DB → Redirect login
```

#### **File liên quan:**
- `Backend/controllers/authController.js` - `register()`
- `Frontend/views/register.pug`

#### **Chi tiết code:**
```javascript
// authController.js - register()
const register = async (req, res) => {
  try {
    const { username, email, password, confirmPassword } = req.body

    // 1. Validation
    if (password !== confirmPassword) {
      return res.render('register', { error: 'Mật khẩu không khớp' })
    }

    // 2. Kiểm tra user đã tồn tại
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    })

    if (existingUser) {
      return res.render('register', { error: 'Email hoặc username đã tồn tại' })
    }

    // 3. Hash password
    const hashedPassword = await bcrypt.hash(password, 10)

    // 4. Tạo user mới
    const user = new User({
      username,
      email,
      password: hashedPassword,
      role: 'customer'  // Mặc định là customer
    })

    await user.save()

    // 5. Redirect đến trang login
    res.redirect('/login?success=Đăng ký thành công')

  } catch (error) {
    console.error('Lỗi đăng ký:', error)
    res.render('register', { error: 'Có lỗi xảy ra' })
  }
}
```

### **2. ĐĂNG NHẬP**

#### **Luồng hoạt động:**
```
User nhập form → Validate credentials → Tạo session → Redirect theo role
```

#### **Chi tiết code:**
```javascript
// authController.js - login()
const login = async (req, res) => {
  try {
    const { email, password } = req.body

    // 1. Tìm user theo email
    const user = await User.findOne({ email })

    if (!user) {
      return res.render('login', { error: 'Email không tồn tại' })
    }

    // 2. Kiểm tra password
    const isValidPassword = await bcrypt.compare(password, user.password)

    if (!isValidPassword) {
      return res.render('login', { error: 'Mật khẩu không đúng' })
    }

    // 3. Tạo session
    req.session.userId = user._id
    req.session.userRole = user.role

    // 4. Redirect theo role
    if (user.role === 'admin') {
      res.redirect('/admin')
    } else {
      res.redirect('/')  // Trang chủ cho customer
    }

  } catch (error) {
    console.error('Lỗi đăng nhập:', error)
    res.render('login', { error: 'Có lỗi xảy ra' })
  }
}
```

### **3. MIDDLEWARE AUTHENTICATION**

#### **File:** `Backend/middleware/authMiddleware.js`

```javascript
// Kiểm tra đăng nhập
const auth = (req, res, next) => {
  if (req.session.userId) {
    next()  // Đã đăng nhập, tiếp tục
  } else {
    res.redirect('/login')  // Chưa đăng nhập, redirect
  }
}

// Kiểm tra quyền admin
const adminAuth = (req, res, next) => {
  if (req.session.userId && req.session.userRole === 'admin') {
    next()
  } else {
    res.status(403).send('Không có quyền truy cập')
  }
}

// Load thông tin user vào request
const loadUser = async (req, res, next) => {
  if (req.session.userId) {
    try {
      const user = await User.findById(req.session.userId).select('-password')
      req.user = user
      res.locals.user = user  // Để sử dụng trong templates
    } catch (error) {
      console.error('Lỗi load user:', error)
    }
  }
  next()
}
```

---

## 🏠 TRANG CHỦ VÀ DUYỆT SẢN PHẨM

### **1. HIỂN THỊ TRANG CHỦ**

#### **Luồng hoạt động:**
```
Request → Load categories → Load featured books → Render homepage
```

#### **File:** `Backend/controllers/bookController.js` - `getHomePage()`

```javascript
const getHomePage = async (req, res) => {
  try {
    // 1. Lấy danh mục sách
    const categories = await Category.find().sort({ name: 1 })

    // 2. Lấy sách nổi bật (sách bán chạy)
    const featuredBooks = await Book.find({ stock: { $gt: 0 } })
      .sort({ sold: -1 })  // Sắp xếp theo số lượng bán
      .limit(8)
      .populate('category', 'name')

    // 3. Lấy sách mới nhất
    const newBooks = await Book.find({ stock: { $gt: 0 } })
      .sort({ createdAt: -1 })
      .limit(8)
      .populate('category', 'name')

    // 4. Render trang chủ
    res.render('index', {
      categories,
      featuredBooks,
      newBooks,
      user: req.user  // Thông tin user nếu đã đăng nhập
    })

  } catch (error) {
    console.error('Lỗi tải trang chủ:', error)
    res.status(500).send('Lỗi server')
  }
}
```

### **2. TÌM KIẾM SÁCH**

#### **Luồng hoạt động:**
```
User nhập từ khóa → AJAX request → Search DB → Return suggestions → Display results
```

#### **File:** `Frontend/public/js/search.js`

```javascript
// Tìm kiếm với suggestions
async function searchBooks(query) {
  try {
    // 1. Gửi AJAX request
    const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`)
    const data = await response.json()

    // 2. Hiển thị suggestions
    displaySuggestions(data.suggestions)

  } catch (error) {
    console.error('Lỗi tìm kiếm:', error)
  }
}

// Hiển thị gợi ý tìm kiếm
function displaySuggestions(suggestions) {
  const suggestionsContainer = document.querySelector('.search-suggestions')

  if (suggestions.length === 0) {
    suggestionsContainer.style.display = 'none'
    return
  }

  // Tạo HTML cho suggestions
  const suggestionsHTML = suggestions.map(book => `
    <div class="suggestion-item" onclick="selectBook('${book._id}')">
      <img src="${book.image || '/public/img/default-book.jpg'}" alt="${book.title}">
      <div class="suggestion-info">
        <h4>${book.title}</h4>
        <p>${book.price.toLocaleString('vi-VN')} đ</p>
      </div>
    </div>
  `).join('')

  suggestionsContainer.innerHTML = suggestionsHTML
  suggestionsContainer.style.display = 'block'
}
```

#### **Backend search API:**
```javascript
// bookController.js - searchBooks()
const searchBooks = async (req, res) => {
  try {
    const { q } = req.query

    if (!q || q.length < 2) {
      return res.json({ suggestions: [] })
    }

    // Tìm kiếm theo title và author
    const books = await Book.find({
      $and: [
        { stock: { $gt: 0 } },  // Còn hàng
        {
          $or: [
            { title: { $regex: q, $options: 'i' } },      // Tìm theo tên
            { author: { $regex: q, $options: 'i' } }      // Tìm theo tác giả
          ]
        }
      ]
    })
    .select('title author image price')
    .limit(5)

    res.json({ suggestions: books })

  } catch (error) {
    console.error('Lỗi tìm kiếm:', error)
    res.status(500).json({ error: 'Lỗi server' })
  }
}
```

### **3. PHÂN TRANG VÀ LỌC SẢN PHẨM**

#### **Luồng hoạt động:**
```
Request với params → Parse filters → Query DB → Calculate pagination → Render results
```

#### **File:** `Backend/controllers/bookController.js` - `getAllBooks()`

```javascript
const getAllBooks = async (req, res) => {
  try {
    // 1. Parse parameters
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 12
    const skip = (page - 1) * limit
    const category = req.query.category
    const sortBy = req.query.sort || 'createdAt'
    const sortOrder = req.query.order === 'asc' ? 1 : -1

    // 2. Tạo filter object
    const filter = { stock: { $gt: 0 } }  // Chỉ lấy sách còn hàng

    if (category && category !== 'all') {
      filter.category = category
    }

    // 3. Query database
    const [books, total, categories] = await Promise.all([
      Book.find(filter)
        .populate('category', 'name')
        .sort({ [sortBy]: sortOrder })
        .skip(skip)
        .limit(limit),
      Book.countDocuments(filter),
      Category.find().sort({ name: 1 })
    ])

    // 4. Calculate pagination
    const totalPages = Math.ceil(total / limit)

    // 5. Render results
    res.render('books', {
      books,
      categories,
      currentCategory: category || 'all',
      currentSort: sortBy,
      currentOrder: req.query.order || 'desc',
      pagination: {
        current: page,
        total: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        next: page + 1,
        prev: page - 1
      },
      user: req.user
    })

  } catch (error) {
    console.error('Lỗi tải danh sách sách:', error)
    res.status(500).send('Lỗi server')
  }
}
```

---

## 🛒 HỆ THỐNG GIỎ HÀNG

### **1. THÊM SẢN PHẨM VÀO GIỎ HÀNG**

#### **Luồng hoạt động:**
```
User click "Thêm vào giỏ" → Check login → Validate stock → Add to cart → Update UI
```

#### **File:** `Backend/controllers/cartController.js` - `addToCart()`

```javascript
const addToCart = async (req, res) => {
  try {
    const bookId = req.params.id
    const quantity = parseInt(req.body.quantity) || 1
    const userId = req.user._id

    // 1. Kiểm tra sách có tồn tại và còn hàng
    const book = await Book.findById(bookId)

    if (!book) {
      return res.status(404).json({ error: 'Sách không tồn tại' })
    }

    if (book.stock < quantity) {
      return res.status(400).json({ error: 'Không đủ hàng trong kho' })
    }

    // 2. Tìm hoặc tạo giỏ hàng
    let cart = await Cart.findOne({ user: userId })

    if (!cart) {
      cart = new Cart({ user: userId, items: [] })
    }

    // 3. Kiểm tra sách đã có trong giỏ chưa
    const existingItemIndex = cart.items.findIndex(
      item => item.book.toString() === bookId
    )

    if (existingItemIndex > -1) {
      // Đã có → tăng số lượng
      const newQuantity = cart.items[existingItemIndex].quantity + quantity

      if (newQuantity > book.stock) {
        return res.status(400).json({ error: 'Vượt quá số lượng trong kho' })
      }

      cart.items[existingItemIndex].quantity = newQuantity
      cart.items[existingItemIndex].price = book.price
    } else {
      // Chưa có → thêm mới
      cart.items.push({
        book: bookId,
        quantity: quantity,
        price: book.price
      })
    }

    // 4. Tính tổng tiền
    cart.totalPrice = cart.items.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)

    // 5. Lưu giỏ hàng
    await cart.save()

    res.json({
      success: true,
      message: 'Đã thêm vào giỏ hàng',
      cartItemCount: cart.items.length
    })

  } catch (error) {
    console.error('Lỗi thêm vào giỏ hàng:', error)
    res.status(500).json({ error: 'Lỗi server' })
  }
}
```

### **2. HIỂN THỊ GIỎ HÀNG**

#### **File:** `Backend/controllers/cartController.js` - `getCart()`

```javascript
const getCart = async (req, res) => {
  try {
    const userId = req.user._id

    // 1. Lấy giỏ hàng với thông tin sách
    const cart = await Cart.findOne({ user: userId })
      .populate({
        path: 'items.book',
        select: 'title image price stock author'
      })

    if (!cart || cart.items.length === 0) {
      return res.render('cart', {
        cart: null,
        user: req.user,
        categories: await Category.find().sort({ name: 1 })
      })
    }

    // 2. Kiểm tra và cập nhật giỏ hàng
    let needUpdate = false
    const validItems = []

    for (let item of cart.items) {
      if (item.book && item.book.stock > 0) {
        // Kiểm tra số lượng không vượt quá stock
        if (item.quantity > item.book.stock) {
          item.quantity = item.book.stock
          needUpdate = true
        }

        // Cập nhật giá nếu có thay đổi
        if (item.price !== item.book.price) {
          item.price = item.book.price
          needUpdate = true
        }

        validItems.push(item)
      } else {
        // Sách đã bị xóa hoặc hết hàng
        needUpdate = true
      }
    }

    // 3. Cập nhật giỏ hàng nếu cần
    if (needUpdate) {
      cart.items = validItems
      cart.totalPrice = validItems.reduce((total, item) => {
        return total + (item.price * item.quantity)
      }, 0)

      await cart.save()
    }

    // 4. Render trang giỏ hàng
    const categories = await Category.find().sort({ name: 1 })

    res.render('cart', {
      cart,
      user: req.user,
      categories
    })

  } catch (error) {
    console.error('Lỗi tải giỏ hàng:', error)
    res.status(500).send('Lỗi server')
  }
}
```

### **3. CẬP NHẬT SỐ LƯỢNG TRONG GIỎ HÀNG**

#### **Frontend JavaScript:**
```javascript
// Frontend/public/js/cart.js
async function updateQuantity(bookId, action) {
  try {
    const endpoint = action === 'increase'
      ? `/cart/increase/${bookId}`
      : `/cart/decrease/${bookId}`

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    const data = await response.json()

    if (data.success) {
      // Cập nhật UI
      location.reload()  // Hoặc cập nhật DOM trực tiếp
    } else {
      alert(data.error || 'Có lỗi xảy ra')
    }

  } catch (error) {
    console.error('Lỗi cập nhật giỏ hàng:', error)
    alert('Có lỗi xảy ra')
  }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
  // Nút tăng số lượng
  document.querySelectorAll('.btn-increase').forEach(btn => {
    btn.addEventListener('click', function() {
      const bookId = this.getAttribute('data-book-id')
      updateQuantity(bookId, 'increase')
    })
  })

  // Nút giảm số lượng
  document.querySelectorAll('.btn-decrease').forEach(btn => {
    btn.addEventListener('click', function() {
      const bookId = this.getAttribute('data-book-id')
      updateQuantity(bookId, 'decrease')
    })
  })

  // Nút xóa sản phẩm
  document.querySelectorAll('.btn-remove').forEach(btn => {
    btn.addEventListener('click', function() {
      const bookId = this.getAttribute('data-book-id')
      if (confirm('Bạn có chắc muốn xóa sản phẩm này?')) {
        removeFromCart(bookId)
      }
    })
  })
})
```

---

## 💳 HỆ THỐNG THANH TOÁN VÀ ĐẶT HÀNG

### **1. TRANG CHECKOUT**

#### **Luồng hoạt động:**
```
User click "Thanh toán" → Load cart → Validate items → Show checkout form → Process order
```

#### **File:** `Backend/controllers/cartController.js` - `checkout()`

```javascript
const checkout = async (req, res) => {
  try {
    const userId = req.user._id

    // 1. Lấy giỏ hàng
    const cart = await Cart.findOne({ user: userId })
      .populate('items.book', 'title image price stock')

    if (!cart || cart.items.length === 0) {
      return res.redirect('/cart?error=Giỏ hàng trống')
    }

    // 2. Kiểm tra tính hợp lệ của giỏ hàng
    const invalidItems = []
    let totalPrice = 0

    for (let item of cart.items) {
      if (!item.book || item.book.stock < item.quantity) {
        invalidItems.push(item)
      } else {
        totalPrice += item.price * item.quantity
      }
    }

    if (invalidItems.length > 0) {
      return res.redirect('/cart?error=Có sản phẩm không hợp lệ trong giỏ hàng')
    }

    // 3. Lấy thông tin user
    const user = await User.findById(userId)

    // 4. Render trang checkout
    const categories = await Category.find().sort({ name: 1 })

    res.render('checkout', {
      cart,
      user,
      categories,
      totalPrice
    })

  } catch (error) {
    console.error('Lỗi tải trang checkout:', error)
    res.redirect('/cart?error=Có lỗi xảy ra')
  }
}
```

### **2. XỬ LÝ ĐẶT HÀNG**

#### **File:** `Backend/controllers/orderController.js` - `createOrder()`

```javascript
const createOrder = async (req, res) => {
  try {
    const userId = req.user._id
    const {
      fullName,
      phone,
      address,
      paymentMethod,
      note
    } = req.body

    // 1. Validation thông tin đặt hàng
    if (!fullName || !phone || !address) {
      return res.redirect('/cart/checkout?error=Vui lòng điền đầy đủ thông tin')
    }

    // 2. Lấy giỏ hàng
    const cart = await Cart.findOne({ user: userId })
      .populate('items.book', 'title price stock')

    if (!cart || cart.items.length === 0) {
      return res.redirect('/cart?error=Giỏ hàng trống')
    }

    // 3. Kiểm tra stock và tính tổng tiền
    let totalPrice = 0
    const orderItems = []

    for (let item of cart.items) {
      if (!item.book || item.book.stock < item.quantity) {
        return res.redirect('/cart?error=Sản phẩm không đủ hàng')
      }

      orderItems.push({
        book: item.book._id,
        quantity: item.quantity,
        price: item.book.price
      })

      totalPrice += item.book.price * item.quantity
    }

    // 4. Tạo mã đơn hàng
    const orderCount = await Order.countDocuments()
    const orderCode = `#OD${(orderCount + 1).toString().padStart(3, '0')}`

    // 5. Tạo đơn hàng mới
    const order = new Order({
      orderCode,
      user: userId,
      items: orderItems,
      totalPrice,
      shippingInfo: {
        fullName,
        phone,
        address
      },
      paymentMethod,
      note,
      status: 'pending'
    })

    await order.save()

    // 6. Cập nhật stock sách
    for (let item of cart.items) {
      await Book.findByIdAndUpdate(item.book._id, {
        $inc: {
          stock: -item.quantity,
          sold: item.quantity
        }
      })
    }

    // 7. Xóa giỏ hàng
    await Cart.findOneAndDelete({ user: userId })

    // 8. Redirect đến trang thành công
    res.redirect(`/order-success?orderId=${order._id}`)

  } catch (error) {
    console.error('Lỗi tạo đơn hàng:', error)
    res.redirect('/cart/checkout?error=Có lỗi xảy ra khi đặt hàng')
  }
}
```

---

## 📦 QUẢN LÝ ĐỚN HÀNG (CUSTOMER)

### **1. DANH SÁCH ĐỚN HÀNG**

#### **File:** `Backend/controllers/orderController.js` - `getMyOrders()`

```javascript
const getMyOrders = async (req, res) => {
  try {
    const userId = req.user._id

    // 1. Parse parameters
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 10
    const skip = (page - 1) * limit

    // 2. Filter theo status
    const statusFilter = req.query.status && req.query.status !== 'all'
      ? { user: userId, status: req.query.status }
      : { user: userId }

    // 3. Query orders
    const [orders, total] = await Promise.all([
      Order.find(statusFilter)
        .select('orderCode items totalPrice status createdAt')
        .populate('items.book', 'title image price')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Order.countDocuments(statusFilter)
    ])

    // 4. Kiểm tra đánh giá cho từng đơn hàng
    const Review = require('../models/reviewModel')

    for (let order of orders) {
      if (order.status === 'delivered') {
        // Lấy tất cả reviews của user cho đơn hàng này
        const orderReviews = await Review.find({
          user: userId,
          order: order._id
        }).lean()

        // Tạo map để tra cứu review theo item
        order.itemReviews = []
        for (let review of orderReviews) {
          const reviewedItem = order.items.find(item =>
            item.book && item.book._id.toString() === review.book.toString()
          )
          if (reviewedItem) {
            order.itemReviews.push({
              itemId: reviewedItem._id,
              reviewId: review._id,
              bookId: review.book
            })
          }
        }
      }
    }

    // 5. Render trang đơn hàng
    const categories = await Category.find().sort({ name: 1 })

    res.render('customer/order/order', {
      orders,
      currentStatus: req.query.status || 'all',
      user: req.user,
      categories,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Lỗi tải đơn hàng:', error)
    res.status(500).render('customer/order/order', {
      orders: [],
      error: 'Không thể tải đơn hàng',
      user: req.user,
      categories: await Category.find().sort({ name: 1 }),
      pagination: { total: 0, page: 1, pages: 0 }
    })
  }
}
```

### **2. HỦY ĐỚN HÀNG**

#### **File:** `Backend/controllers/orderController.js` - `cancelOrder()`

```javascript
const cancelOrder = async (req, res) => {
  try {
    const orderId = req.params.orderId
    const userId = req.user._id

    // 1. Tìm đơn hàng
    const order = await Order.findOne({ _id: orderId, user: userId })

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy đơn hàng'
      })
    }

    // 2. Kiểm tra trạng thái
    if (order.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Chỉ có thể hủy đơn hàng đang chờ xác nhận'
      })
    }

    // 3. Cập nhật trạng thái
    order.status = 'cancelled'
    await order.save()

    // 4. Hoàn lại stock
    for (let item of order.items) {
      await Book.findByIdAndUpdate(item.book, {
        $inc: {
          stock: item.quantity,
          sold: -item.quantity
        }
      })
    }

    res.json({
      success: true,
      message: 'Đơn hàng đã được hủy thành công'
    })

  } catch (error) {
    console.error('Lỗi hủy đơn hàng:', error)
    res.status(500).json({
      success: false,
      message: 'Lỗi server'
    })
  }
}
```

---

## ⭐ HỆ THỐNG ĐÁNH GIÁ

### **1. ĐÁNH GIÁ SẢN PHẨM**

#### **File:** `Backend/controllers/reviewController.js` - `addReviewFromOrder()`

```javascript
const addReviewFromOrder = async (req, res) => {
  try {
    const { orderItemId, rating, content } = req.body
    const userId = req.user._id

    // 1. Tìm order chứa orderItemId
    const order = await Order.findOne({
      user: userId,
      'items._id': orderItemId,
      status: 'delivered'
    })

    if (!order) {
      return res.redirect('/customer/orders?error=Không tìm thấy đơn hàng')
    }

    // 2. Tìm item cụ thể
    const orderItem = order.items.find(item =>
      item._id.toString() === orderItemId
    )

    if (!orderItem) {
      return res.redirect('/customer/orders?error=Không tìm thấy sản phẩm')
    }

    const bookId = orderItem.book

    // 3. Kiểm tra đã đánh giá chưa
    const existingReview = await Review.findOne({
      user: userId,
      order: order._id,
      book: bookId
    })

    if (existingReview) {
      return res.redirect('/customer/orders?error=Đã đánh giá sản phẩm này rồi')
    }

    // 4. Tạo đánh giá mới
    const review = new Review({
      user: userId,
      book: bookId,
      order: order._id,
      rating: parseInt(rating),
      comment: content
    })

    await review.save()

    res.redirect('/customer/orders?success=Đánh giá thành công')

  } catch (error) {
    console.error('Lỗi đánh giá:', error)
    res.redirect('/customer/orders?error=Có lỗi xảy ra')
  }
}
```

### **2. XEM CHI TIẾT ĐÁNH GIÁ**

#### **File:** `Backend/controllers/reviewController.js` - `getReviewById()`

```javascript
const getReviewById = async (req, res) => {
  try {
    const reviewId = req.params.reviewId

    // 1. Lấy thông tin đánh giá
    const review = await Review.findById(reviewId)
      .populate('user', 'username')
      .populate('book', 'title author image price')
      .populate('order', 'createdAt totalPrice')
      .lean()

    if (!review) {
      return res.redirect('/customer/orders?error=Không tìm thấy đánh giá')
    }

    // 2. Kiểm tra quyền xem
    if (review.user._id.toString() !== req.user._id.toString()) {
      return res.redirect('/customer/orders?error=Không có quyền xem')
    }

    // 3. Render trang xem đánh giá
    res.render('customer/review/view-review', {
      review,
      user: req.user
    })

  } catch (error) {
    console.error('Lỗi xem đánh giá:', error)
    res.redirect('/customer/orders?error=Có lỗi xảy ra')
  }
}
```

---

## 👨‍💼 HỆ THỐNG ADMIN

### **1. DASHBOARD ADMIN**

#### **File:** `Backend/controllers/adminController.js` - `getDashboard()`

```javascript
const getDashboard = async (req, res) => {
  try {
    // 1. Thống kê tổng quan
    const [
      totalBooks,
      totalOrders,
      totalUsers,
      totalRevenue,
      pendingOrders,
      recentOrders
    ] = await Promise.all([
      Book.countDocuments(),
      Order.countDocuments(),
      User.countDocuments({ role: 'customer' }),
      Order.aggregate([
        { $match: { status: 'delivered' } },
        { $group: { _id: null, total: { $sum: '$totalPrice' } } }
      ]),
      Order.countDocuments({ status: 'pending' }),
      Order.find()
        .populate('user', 'username email')
        .sort({ createdAt: -1 })
        .limit(5)
        .lean()
    ])

    // 2. Thống kê theo tháng
    const monthlyStats = await Order.aggregate([
      {
        $match: {
          status: 'delivered',
          createdAt: {
            $gte: new Date(new Date().getFullYear(), 0, 1) // Từ đầu năm
          }
        }
      },
      {
        $group: {
          _id: { $month: '$createdAt' },
          revenue: { $sum: '$totalPrice' },
          orders: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ])

    // 3. Top sách bán chạy
    const topBooks = await Book.find()
      .sort({ sold: -1 })
      .limit(5)
      .select('title sold image')

    res.render('admin/dashboard', {
      stats: {
        totalBooks,
        totalOrders,
        totalUsers,
        totalRevenue: totalRevenue[0]?.total || 0,
        pendingOrders
      },
      recentOrders,
      monthlyStats,
      topBooks,
      user: req.user
    })

  } catch (error) {
    console.error('Lỗi tải dashboard:', error)
    res.status(500).send('Lỗi server')
  }
}
```

### **2. QUẢN LÝ SÁCH**

#### **Thêm sách mới:**
```javascript
const addBook = async (req, res) => {
  try {
    const {
      title,
      author,
      category,
      price,
      stock,
      description
    } = req.body

    // 1. Validation
    if (!title || !author || !category || !price || !stock) {
      return res.redirect('/admin/book/add?error=Vui lòng điền đầy đủ thông tin')
    }

    // 2. Xử lý upload ảnh
    let imagePath = '/public/img/default-book.jpg'
    if (req.file) {
      imagePath = `/uploads/${req.file.filename}`
    }

    // 3. Tạo sách mới
    const book = new Book({
      title,
      author,
      category,
      price: parseFloat(price),
      stock: parseInt(stock),
      description,
      image: imagePath,
      sold: 0
    })

    await book.save()

    res.redirect('/admin/book?success=Thêm sách thành công')

  } catch (error) {
    console.error('Lỗi thêm sách:', error)
    res.redirect('/admin/book/add?error=Có lỗi xảy ra')
  }
}
```

#### **Cập nhật sách:**
```javascript
const updateBook = async (req, res) => {
  try {
    const bookId = req.params.id
    const updateData = req.body

    // 1. Tìm sách
    const book = await Book.findById(bookId)
    if (!book) {
      return res.redirect('/admin/book?error=Không tìm thấy sách')
    }

    // 2. Xử lý ảnh mới (nếu có)
    if (req.file) {
      updateData.image = `/uploads/${req.file.filename}`

      // Xóa ảnh cũ (nếu không phải ảnh mặc định)
      if (book.image && book.image !== '/public/img/default-book.jpg') {
        const oldImagePath = path.join(__dirname, '../../Frontend', book.image)
        if (fs.existsSync(oldImagePath)) {
          fs.unlinkSync(oldImagePath)
        }
      }
    }

    // 3. Cập nhật sách
    await Book.findByIdAndUpdate(bookId, updateData)

    res.redirect('/admin/book?success=Cập nhật sách thành công')

  } catch (error) {
    console.error('Lỗi cập nhật sách:', error)
    res.redirect('/admin/book?error=Có lỗi xảy ra')
  }
}
```

### **3. QUẢN LÝ ĐỚN HÀNG**

#### **Cập nhật trạng thái đơn hàng:**
```javascript
const updateOrderStatus = async (req, res) => {
  try {
    const orderId = req.params.id
    const { status } = req.body

    // 1. Tìm đơn hàng
    const order = await Order.findById(orderId)
    if (!order) {
      return res.redirect('/admin/order?error=Không tìm thấy đơn hàng')
    }

    // 2. Kiểm tra trạng thái hợp lệ
    const validStatuses = ['pending', 'transporting', 'delivered', 'cancelled']
    if (!validStatuses.includes(status)) {
      return res.redirect('/admin/order?error=Trạng thái không hợp lệ')
    }

    // 3. Xử lý logic đặc biệt
    if (status === 'cancelled' && order.status !== 'cancelled') {
      // Hoàn lại stock khi hủy đơn
      for (let item of order.items) {
        await Book.findByIdAndUpdate(item.book, {
          $inc: {
            stock: item.quantity,
            sold: -item.quantity
          }
        })
      }
    }

    // 4. Cập nhật trạng thái
    order.status = status
    await order.save()

    res.redirect('/admin/order?success=Cập nhật trạng thái thành công')

  } catch (error) {
    console.error('Lỗi cập nhật đơn hàng:', error)
    res.redirect('/admin/order?error=Có lỗi xảy ra')
  }
}
```

---

## 🔍 HỆ THỐNG TÌM KIẾM NÂNG CAO

### **1. SEARCH API**

#### **File:** `Backend/controllers/bookController.js` - `searchBooks()`

```javascript
const searchBooks = async (req, res) => {
  try {
    const { q, category, minPrice, maxPrice, sort } = req.query

    // 1. Tạo filter object
    const filter = { stock: { $gt: 0 } }

    // Tìm kiếm theo từ khóa
    if (q && q.length >= 2) {
      filter.$or = [
        { title: { $regex: q, $options: 'i' } },
        { author: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } }
      ]
    }

    // Lọc theo danh mục
    if (category && category !== 'all') {
      filter.category = category
    }

    // Lọc theo giá
    if (minPrice || maxPrice) {
      filter.price = {}
      if (minPrice) filter.price.$gte = parseFloat(minPrice)
      if (maxPrice) filter.price.$lte = parseFloat(maxPrice)
    }

    // 2. Tạo sort object
    let sortObj = { createdAt: -1 } // Mặc định sắp xếp theo ngày tạo

    switch (sort) {
      case 'price_asc':
        sortObj = { price: 1 }
        break
      case 'price_desc':
        sortObj = { price: -1 }
        break
      case 'name_asc':
        sortObj = { title: 1 }
        break
      case 'name_desc':
        sortObj = { title: -1 }
        break
      case 'popular':
        sortObj = { sold: -1 }
        break
    }

    // 3. Pagination
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 12
    const skip = (page - 1) * limit

    // 4. Query database
    const [books, total, categories] = await Promise.all([
      Book.find(filter)
        .populate('category', 'name')
        .sort(sortObj)
        .skip(skip)
        .limit(limit)
        .lean(),
      Book.countDocuments(filter),
      Category.find().sort({ name: 1 })
    ])

    // 5. Response
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      // AJAX request
      res.json({
        books,
        pagination: {
          current: page,
          total: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      })
    } else {
      // Normal request
      res.render('search-results', {
        books,
        categories,
        searchQuery: q,
        currentCategory: category,
        currentSort: sort,
        filters: { minPrice, maxPrice },
        pagination: {
          current: page,
          total: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        },
        user: req.user
      })
    }

  } catch (error) {
    console.error('Lỗi tìm kiếm:', error)
    res.status(500).json({ error: 'Lỗi server' })
  }
}
```

### **2. AUTOCOMPLETE SUGGESTIONS**

#### **Frontend JavaScript:**
```javascript
// Frontend/public/js/search.js
let searchTimeout
let currentRequest

async function handleSearch(input) {
  const query = input.value.trim()

  // Clear previous timeout
  clearTimeout(searchTimeout)

  // Cancel previous request
  if (currentRequest) {
    currentRequest.abort()
  }

  if (query.length < 2) {
    hideSuggestions()
    return
  }

  // Debounce search
  searchTimeout = setTimeout(async () => {
    try {
      // Create AbortController for cancellation
      const controller = new AbortController()
      currentRequest = controller

      const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(query)}`, {
        signal: controller.signal
      })

      if (!response.ok) throw new Error('Network error')

      const data = await response.json()
      displaySuggestions(data.suggestions, query)

    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Search error:', error)
      }
    } finally {
      currentRequest = null
    }
  }, 300) // 300ms debounce
}

function displaySuggestions(suggestions, query) {
  const container = document.querySelector('.search-suggestions')

  if (!suggestions || suggestions.length === 0) {
    hideSuggestions()
    return
  }

  // Highlight matching text
  const highlightText = (text, query) => {
    const regex = new RegExp(`(${query})`, 'gi')
    return text.replace(regex, '<mark>$1</mark>')
  }

  const suggestionsHTML = suggestions.map(book => `
    <div class="suggestion-item" onclick="selectBook('${book._id}')">
      <img src="${book.image || '/public/img/default-book.jpg'}"
           alt="${book.title}"
           loading="lazy">
      <div class="suggestion-info">
        <h4>${highlightText(book.title, query)}</h4>
        <p class="author">${highlightText(book.author, query)}</p>
        <p class="price">${book.price.toLocaleString('vi-VN')} đ</p>
      </div>
    </div>
  `).join('')

  container.innerHTML = suggestionsHTML
  container.style.display = 'block'
}

function hideSuggestions() {
  const container = document.querySelector('.search-suggestions')
  container.style.display = 'none'
}

function selectBook(bookId) {
  window.location.href = `/books/${bookId}`
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.querySelector('#search-input')

  if (searchInput) {
    searchInput.addEventListener('input', function() {
      handleSearch(this)
    })

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
      if (!e.target.closest('.search-container')) {
        hideSuggestions()
      }
    })

    // Handle keyboard navigation
    searchInput.addEventListener('keydown', function(e) {
      const suggestions = document.querySelectorAll('.suggestion-item')
      let currentIndex = -1

      // Find currently selected suggestion
      suggestions.forEach((item, index) => {
        if (item.classList.contains('selected')) {
          currentIndex = index
        }
      })

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          currentIndex = Math.min(currentIndex + 1, suggestions.length - 1)
          updateSelection(suggestions, currentIndex)
          break

        case 'ArrowUp':
          e.preventDefault()
          currentIndex = Math.max(currentIndex - 1, -1)
          updateSelection(suggestions, currentIndex)
          break

        case 'Enter':
          e.preventDefault()
          if (currentIndex >= 0) {
            suggestions[currentIndex].click()
          } else {
            // Submit search form
            document.querySelector('#search-form').submit()
          }
          break

        case 'Escape':
          hideSuggestions()
          break
      }
    })
  }
})

function updateSelection(suggestions, selectedIndex) {
  suggestions.forEach((item, index) => {
    if (index === selectedIndex) {
      item.classList.add('selected')
    } else {
      item.classList.remove('selected')
    }
  })
}
```

---

## 📊 HỆ THỐNG THỐNG KÊ VÀ BÁO CÁO

### **1. THỐNG KÊ DOANH THU**

#### **File:** `Backend/controllers/adminController.js` - `getSalesData()`

```javascript
const getSalesData = async (req, res) => {
  try {
    const { startDate, endDate, period = 'month' } = req.query

    // 1. Tạo date range
    let dateFilter = { status: 'delivered' }

    if (startDate && endDate) {
      dateFilter.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    } else {
      // Mặc định lấy 12 tháng gần nhất
      const twelveMonthsAgo = new Date()
      twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12)
      dateFilter.createdAt = { $gte: twelveMonthsAgo }
    }

    // 2. Tạo group stage theo period
    let groupStage
    switch (period) {
      case 'day':
        groupStage = {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          }
        }
        break
      case 'week':
        groupStage = {
          _id: {
            year: { $year: '$createdAt' },
            week: { $week: '$createdAt' }
          }
        }
        break
      case 'month':
      default:
        groupStage = {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          }
        }
        break
    }

    // 3. Thống kê doanh thu
    const [revenueStats, topBooks, topCategories] = await Promise.all([
      Order.aggregate([
        { $match: dateFilter },
        {
          $group: {
            ...groupStage,
            revenue: { $sum: '$totalPrice' },
            orders: { $sum: 1 },
            avgOrderValue: { $avg: '$totalPrice' }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ]),

      // Top sách bán chạy
      Order.aggregate([
        { $match: dateFilter },
        { $unwind: '$items' },
        {
          $group: {
            _id: '$items.book',
            totalSold: { $sum: '$items.quantity' },
            revenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } }
          }
        },
        {
          $lookup: {
            from: 'books',
            localField: '_id',
            foreignField: '_id',
            as: 'book'
          }
        },
        { $unwind: '$book' },
        { $sort: { totalSold: -1 } },
        { $limit: 10 }
      ]),

      // Top danh mục
      Order.aggregate([
        { $match: dateFilter },
        { $unwind: '$items' },
        {
          $lookup: {
            from: 'books',
            localField: 'items.book',
            foreignField: '_id',
            as: 'book'
          }
        },
        { $unwind: '$book' },
        {
          $group: {
            _id: '$book.category',
            totalSold: { $sum: '$items.quantity' },
            revenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } }
          }
        },
        {
          $lookup: {
            from: 'categories',
            localField: '_id',
            foreignField: '_id',
            as: 'category'
          }
        },
        { $unwind: '$category' },
        { $sort: { revenue: -1 } },
        { $limit: 5 }
      ])
    ])

    // 4. Tính tổng kết
    const totalRevenue = revenueStats.reduce((sum, item) => sum + item.revenue, 0)
    const totalOrders = revenueStats.reduce((sum, item) => sum + item.orders, 0)
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0

    res.render('admin/sales', {
      revenueStats,
      topBooks,
      topCategories,
      summary: {
        totalRevenue,
        totalOrders,
        avgOrderValue
      },
      filters: { startDate, endDate, period },
      user: req.user
    })

  } catch (error) {
    console.error('Lỗi thống kê doanh thu:', error)
    res.status(500).send('Lỗi server')
  }
}
```

---

## 🔄 WORKFLOW VÀ ROUTING

### **1. MAIN ROUTES STRUCTURE**

#### **File:** `Backend/server.js`

```javascript
const express = require('express')
const session = require('express-session')
const MongoStore = require('connect-mongo')
const path = require('path')

const app = express()

// 1. Middleware setup
app.use(express.json())
app.use(express.urlencoded({ extended: true }))
app.use(express.static(path.join(__dirname, '../Frontend/public')))
app.use('/uploads', express.static(path.join(__dirname, '../Frontend/uploads')))

// 2. Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'bookstore-secret-key',
  resave: false,
  saveUninitialized: false,
  store: MongoStore.create({
    mongoUrl: process.env.MONGODB_URI || 'mongodb://localhost:27017/bookstore'
  }),
  cookie: {
    secure: false, // Set to true in production with HTTPS
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}))

// 3. View engine setup
app.set('view engine', 'pug')
app.set('views', path.join(__dirname, '../Frontend/views'))

// 4. Routes
const authRoutes = require('./routes/authRoute')
const bookRoutes = require('./routes/bookRoute')
const cartRoutes = require('./routes/cartRoute')
const orderRoutes = require('./routes/orderRoute')
const reviewRoutes = require('./routes/reviewRoute')
const userRoutes = require('./routes/userRoute')
const adminRoutes = require('./routes/adminRoute')

// 5. Route mounting
app.use('/', authRoutes)           // Authentication routes
app.use('/', bookRoutes)           // Book browsing routes
app.use('/cart', cartRoutes)       // Cart management
app.use('/orders', orderRoutes)    // Order processing
app.use('/reviews', reviewRoutes)  // Review system
app.use('/', userRoutes)           // User profile & orders
app.use('/admin', adminRoutes)     // Admin panel

// 6. Error handling
app.use((req, res) => {
  res.status(404).render('404', {
    message: 'Trang không tồn tại',
    user: req.user
  })
})

app.use((error, req, res, next) => {
  console.error('Server error:', error)
  res.status(500).render('500', {
    message: 'Lỗi server',
    user: req.user
  })
})

// 7. Start server
const PORT = process.env.PORT || 5000
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)
})
```

### **2. COMPLETE ROUTE MAP**

```
📁 AUTHENTICATION ROUTES (/routes/authRoute.js)
├── GET  /login              # Trang đăng nhập
├── POST /login              # Xử lý đăng nhập
├── GET  /register           # Trang đăng ký
├── POST /register           # Xử lý đăng ký
└── POST /logout             # Đăng xuất

📁 BOOK ROUTES (/routes/bookRoute.js)
├── GET  /                   # Trang chủ
├── GET  /books              # Danh sách sách
├── GET  /books/:id          # Chi tiết sách
├── GET  /search             # Tìm kiếm sách
├── GET  /api/search         # API tìm kiếm
└── GET  /category/:id       # Sách theo danh mục

📁 CART ROUTES (/routes/cartRoute.js)
├── GET  /cart               # Xem giỏ hàng
├── POST /cart/add/:id       # Thêm vào giỏ hàng
├── POST /cart/increase/:id  # Tăng số lượng
├── POST /cart/decrease/:id  # Giảm số lượng
├── POST /cart/remove/:id    # Xóa khỏi giỏ hàng
└── GET  /cart/checkout      # Trang thanh toán

📁 ORDER ROUTES (/routes/orderRoute.js)
├── POST /orders/create      # Tạo đơn hàng
├── GET  /order-success      # Trang đặt hàng thành công
└── POST /orders/:id/buy-again # Mua lại

📁 USER ROUTES (/routes/userRoute.js)
├── GET  /customer           # Trang cá nhân
├── GET  /customer/orders    # Danh sách đơn hàng
├── GET  /customer/address   # Địa chỉ giao hàng
├── POST /customer/update    # Cập nhật thông tin
├── POST /customer/address/update # Cập nhật địa chỉ
├── POST /customer/orders/:id/cancel # Hủy đơn hàng
└── GET  /reviews/:id        # Xem đánh giá

📁 REVIEW ROUTES (/routes/reviewRoute.js)
├── POST /reviews/add        # Thêm đánh giá từ đơn hàng
├── GET  /reviews/:bookId    # Lấy đánh giá của sách
└── POST /reviews/:bookId    # Đánh giá sách (legacy)

📁 ADMIN ROUTES (/routes/adminRoute.js)
├── GET  /admin              # Dashboard admin
├── GET  /admin/book         # Quản lý sách
├── GET  /admin/book/add     # Thêm sách
├── POST /admin/book/add     # Xử lý thêm sách
├── GET  /admin/book/edit/:id # Sửa sách
├── POST /admin/book/edit/:id # Xử lý sửa sách
├── DELETE /admin/book/delete/:id # Xóa sách
├── GET  /admin/category     # Quản lý danh mục
├── GET  /admin/order        # Quản lý đơn hàng
├── POST /admin/order/update-status/:id # Cập nhật trạng thái
└── GET  /admin/sale         # Thống kê doanh thu
```

---

## 🔒 SECURITY VÀ VALIDATION

### **1. INPUT VALIDATION**

```javascript
// Validation helper functions
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const validatePhone = (phone) => {
  const phoneRegex = /^[0-9]{10,11}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

const validatePassword = (password) => {
  return password && password.length >= 6
}

const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input

  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 1000)   // Limit length
}

// Middleware validation
const validateRegistration = (req, res, next) => {
  const { username, email, password, confirmPassword } = req.body
  const errors = []

  if (!username || username.length < 3) {
    errors.push('Username phải có ít nhất 3 ký tự')
  }

  if (!validateEmail(email)) {
    errors.push('Email không hợp lệ')
  }

  if (!validatePassword(password)) {
    errors.push('Mật khẩu phải có ít nhất 6 ký tự')
  }

  if (password !== confirmPassword) {
    errors.push('Mật khẩu xác nhận không khớp')
  }

  if (errors.length > 0) {
    return res.render('register', { errors })
  }

  // Sanitize inputs
  req.body.username = sanitizeInput(username)
  req.body.email = sanitizeInput(email)

  next()
}
```

### **2. FILE UPLOAD SECURITY**

```javascript
const multer = require('multer')
const path = require('path')

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../../Frontend/uploads'))
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
  }
})

// File filter for security
const fileFilter = (req, file, cb) => {
  // Only allow image files
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true)
  } else {
    cb(new Error('Chỉ cho phép upload file ảnh (JPEG, PNG, GIF)'), false)
  }
}

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
})

module.exports = upload
```

---

## 📱 RESPONSIVE DESIGN VÀ UX

### **1. CSS RESPONSIVE FRAMEWORK**

```css
/* Frontend/public/css/responsive.css */

/* Mobile First Approach */
.container {
  width: 100%;
  padding: 0 15px;
  margin: 0 auto;
}

/* Tablet */
@media (min-width: 768px) {
  .container {
    max-width: 750px;
  }

  .product-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .container {
    max-width: 1200px;
  }

  .product-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .sidebar {
    display: block;
  }
}

/* Mobile Navigation */
.mobile-menu {
  display: none;
}

@media (max-width: 767px) {
  .desktop-menu {
    display: none;
  }

  .mobile-menu {
    display: block;
  }

  .search-suggestions {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    max-height: 50vh;
    overflow-y: auto;
  }
}
```

### **2. PROGRESSIVE WEB APP FEATURES**

```javascript
// Frontend/public/js/pwa.js

// Service Worker Registration
if ('serviceWorker' in navigator) {
  window.addEventListener('load', function() {
    navigator.serviceWorker.register('/sw.js')
      .then(function(registration) {
        console.log('SW registered: ', registration)
      })
      .catch(function(registrationError) {
        console.log('SW registration failed: ', registrationError)
      })
  })
}

// Add to Home Screen
let deferredPrompt
const addBtn = document.querySelector('.add-button')

window.addEventListener('beforeinstallprompt', (e) => {
  // Prevent Chrome 67 and earlier from automatically showing the prompt
  e.preventDefault()
  // Stash the event so it can be triggered later
  deferredPrompt = e
  // Update UI to notify the user they can add to home screen
  addBtn.style.display = 'block'
})

addBtn.addEventListener('click', (e) => {
  // Hide the app provided install promotion
  addBtn.style.display = 'none'
  // Show the install prompt
  deferredPrompt.prompt()
  // Wait for the user to respond to the prompt
  deferredPrompt.userChoice.then((choiceResult) => {
    if (choiceResult.outcome === 'accepted') {
      console.log('User accepted the A2HS prompt')
    } else {
      console.log('User dismissed the A2HS prompt')
    }
    deferredPrompt = null
  })
})
```

---

## 🎯 PERFORMANCE OPTIMIZATION

### **1. DATABASE INDEXING**

```javascript
// Backend/models/bookModel.js
const mongoose = require('mongoose')

const bookSchema = new mongoose.Schema({
  title: { type: String, required: true, index: true },
  author: { type: String, required: true, index: true },
  category: { type: mongoose.Schema.Types.ObjectId, ref: 'Category', index: true },
  price: { type: Number, required: true, index: true },
  stock: { type: Number, required: true, index: true },
  sold: { type: Number, default: 0, index: true },
  description: String,
  image: String,
  createdAt: { type: Date, default: Date.now, index: true }
})

// Compound indexes for common queries
bookSchema.index({ category: 1, price: 1 })
bookSchema.index({ title: 'text', author: 'text', description: 'text' })
bookSchema.index({ stock: 1, sold: -1 })

module.exports = mongoose.model('Book', bookSchema)
```

### **2. CACHING STRATEGY**

```javascript
// Backend/middleware/cache.js
const NodeCache = require('node-cache')
const cache = new NodeCache({ stdTTL: 600 }) // 10 minutes default

const cacheMiddleware = (duration = 600) => {
  return (req, res, next) => {
    const key = req.originalUrl || req.url
    const cachedResponse = cache.get(key)

    if (cachedResponse) {
      return res.send(cachedResponse)
    }

    // Override res.send to cache the response
    const originalSend = res.send
    res.send = function(body) {
      cache.set(key, body, duration)
      originalSend.call(this, body)
    }

    next()
  }
}

// Usage in routes
router.get('/books', cacheMiddleware(300), getAllBooks) // Cache for 5 minutes
router.get('/categories', cacheMiddleware(3600), getCategories) // Cache for 1 hour
```

---

## 🎉 TỔNG KẾT

### **🏗️ KIẾN TRÚC TỔNG THỂ:**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FRONTEND      │    │    BACKEND      │    │    DATABASE     │
│                 │    │                 │    │                 │
│ • Pug Templates │◄──►│ • Express.js    │◄──►│ • MongoDB       │
│ • Vanilla JS    │    │ • Controllers   │    │ • Mongoose ODM  │
│ • CSS3          │    │ • Middleware    │    │ • Indexes       │
│ • Responsive    │    │ • Routes        │    │ • Aggregation   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **🔄 LUỒNG DỮ LIỆU CHÍNH:**

1. **User Request** → **Routes** → **Middleware** → **Controllers**
2. **Controllers** → **Models** → **Database** → **Response**
3. **Templates** → **Rendered HTML** → **User Browser**

### **✅ TÍNH NĂNG HOÀN CHỈNH:**

🔐 **Authentication**: Đăng ký, đăng nhập, phân quyền
🏠 **Homepage**: Hiển thị sách nổi bật, danh mục
🔍 **Search**: Tìm kiếm với autocomplete, filter
📚 **Product Catalog**: Danh sách, chi tiết, phân trang
🛒 **Shopping Cart**: Thêm, sửa, xóa sản phẩm
💳 **Checkout**: Đặt hàng, thanh toán
📦 **Order Management**: Theo dõi, hủy đơn hàng
⭐ **Review System**: Đánh giá từng sản phẩm riêng biệt
👨‍💼 **Admin Panel**: Quản lý sách, đơn hàng, thống kê
📊 **Analytics**: Báo cáo doanh thu, top sản phẩm

### **🚀 PERFORMANCE & SECURITY:**

✅ **Database Optimization**: Indexes, aggregation, lean queries
✅ **Caching**: In-memory cache cho data ít thay đổi
✅ **Input Validation**: Sanitization, type checking
✅ **File Upload Security**: Type validation, size limits
✅ **Session Management**: Secure cookies, MongoDB store
✅ **Error Handling**: Comprehensive error management
✅ **Responsive Design**: Mobile-first approach

Đây là một hệ thống e-commerce hoàn chỉnh, production-ready với architecture vững chắc và user experience tốt! 🎯
